"use strict";
const { Client } = require('@modelcontextprotocol/sdk');
const { SSEClientTransport } = require('@modelcontextprotocol/sdk/transports');
async function main() {
    const client = new Client({
        name: "test-client",
        version: "1.0.0"
    }, {
        capabilities: {}
    });
    console.log('Creating SSE transport...');
    const transport = new SSEClientTransport(
        new URL("http://localhost:3000/sse")
    );
    console.log('Connecting to server...');
    try {
        await client.connect(transport);
        console.log('Successfully connected to server');
        // Test directory listing
        const result = await client.invoke('listDirectory', {
            directoryPath: '.'
        });
        console.log('Directory listing result:', result);
    }
    catch (error) {
        console.error('Error:', error);
    }
    finally {
        await client.disconnect();
    }
}
main().catch(console.error);
