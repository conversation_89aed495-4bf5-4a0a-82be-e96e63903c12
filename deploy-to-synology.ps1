#!/usr/bin/env pwsh
# Deploy MCP Servers to Synology NAS
# This script creates deployment packages and instructions for Synology Docker

param (
    [Parameter()]
    [string]$SynologyHost = "************",
    
    [Parameter()]
    [string]$SynologyPort = "7031",
    
    [Parameter()]
    [string]$OutputDir = "synology-deployment",
    
    [Parameter()]
    [switch]$CreatePackage = $false,
    
    [Parameter()]
    [switch]$ShowInstructions = $false
)

Write-Host "🚀 Synology NAS MCP Deployment Tool" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host "Target: https://$SynologyHost`:$SynologyPort" -ForegroundColor Yellow
Write-Host ""

# MCP Servers for Synology deployment
$mcpServers = @(
    @{
        Name = "MarkItDown MCP"
        Directory = "markitdown-mcp-server"
        Port = "3001"
        Description = "Document conversion with OCR"
        Priority = 1
    },
    @{
        Name = "Filesystem MCP"
        Directory = "fs-mcp-server"
        Port = "3012"
        Description = "File system operations"
        Priority = 2
    },
    @{
        Name = "Outlook MCP"
        Directory = "outlook-mcp-server"
        Port = "3002"
        Description = "Email integration"
        Priority = 3
    },
    @{
        Name = "Google AI Edge MCP"
        Directory = "google-ai-edge-mcp-server"
        Port = "3005"
        Description = "AI processing"
        Priority = 4
    },
    @{
        Name = "OpenManus MCP"
        Directory = "openmanus-mcp-server"
        Port = "3013"
        Description = "FoundationAgents integration"
        Priority = 5
    }
)

function Create-SynologyDeploymentPackage {
    Write-Host "📦 Creating Synology deployment package..." -ForegroundColor Blue
    
    # Clean and create output directory
    if (Test-Path $OutputDir) {
        Remove-Item $OutputDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $OutputDir | Out-Null
    
    $successCount = 0
    
    foreach ($server in $mcpServers) {
        Write-Host "  📁 Packaging $($server.Name)..." -ForegroundColor Yellow
        
        $sourceDir = $server.Directory
        $targetDir = Join-Path $OutputDir $server.Directory
        
        if (Test-Path $sourceDir) {
            # Copy server directory
            Copy-Item $sourceDir $targetDir -Recurse -Force
            
            # Create Synology-specific docker-compose.yml
            $composePath = Join-Path $targetDir "docker-compose.yml"
            if (Test-Path $composePath) {
                $composeContent = Get-Content $composePath -Raw
                
                # Update for Synology environment
                $updatedContent = $composeContent -replace 'OLLAMA_BASE_URL=http://192\.168\.1\.26:11434', "OLLAMA_BASE_URL=http://$SynologyHost`:11434"
                $updatedContent = $updatedContent -replace 'host\.docker\.internal', $SynologyHost
                
                # Ensure proper network configuration
                if ($updatedContent -notmatch 'networks:') {
                    $updatedContent += "`n`nnetworks:`n  mcp-network:`n    driver: bridge"
                }
                
                Set-Content $composePath $updatedContent
                Write-Host "    ✅ Updated docker-compose.yml" -ForegroundColor Green
            }
            
            # Create individual deployment script
            $deployScript = @"
#!/bin/bash
# Deploy $($server.Name) to Synology
echo "Deploying $($server.Name)..."

# Create network if it doesn't exist
docker network create mcp-network 2>/dev/null || true

# Stop existing container
docker stop $($server.Directory) 2>/dev/null || true
docker rm $($server.Directory) 2>/dev/null || true

# Build and start
cd $($server.Directory)
docker-compose up --build -d

# Check status
sleep 5
if docker ps | grep -q $($server.Directory); then
    echo "✅ $($server.Name) deployed successfully"
    echo "🌐 Available at: http://$SynologyHost`:$($server.Port)"
else
    echo "❌ $($server.Name) deployment failed"
    docker logs $($server.Directory) --tail 10
fi
"@
            
            Set-Content (Join-Path $targetDir "deploy.sh") $deployScript
            
            $successCount++
            Write-Host "    ✅ Packaged successfully" -ForegroundColor Green
        } else {
            Write-Host "    ❌ Source directory not found: $sourceDir" -ForegroundColor Red
        }
    }
    
    # Create master deployment script
    $masterScript = @"
#!/bin/bash
# Master MCP Deployment Script for Synology NAS
echo "🚀 Deploying MCP Servers to Synology NAS"
echo "========================================"

# Create shared network
echo "📡 Creating MCP network..."
docker network create mcp-network 2>/dev/null || true

# Deploy servers in priority order
"@
    
    foreach ($server in ($mcpServers | Sort-Object Priority)) {
        $masterScript += @"

echo ""
echo "📦 Deploying $($server.Name)..."
cd $($server.Directory)
chmod +x deploy.sh
./deploy.sh
cd ..
"@
    }
    
    $masterScript += @"

echo ""
echo "🎯 Deployment Summary:"
echo "====================="
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "network=mcp-network"

echo ""
echo "✅ MCP deployment completed!"
echo "🌐 Access your Synology Docker interface at: https://$SynologyHost`:$SynologyPort"
"@
    
    Set-Content (Join-Path $OutputDir "deploy-all.sh") $masterScript
    
    # Create Windows batch file for easy execution
    $windowsScript = @"
@echo off
echo Connecting to Synology NAS and deploying MCP servers...
echo.

REM You can use one of these methods:

echo Method 1: SSH to Synology and run deployment
echo ssh admin@$SynologyHost
echo cd /volume1/docker/mcp-deployment
echo chmod +x deploy-all.sh
echo ./deploy-all.sh
echo.

echo Method 2: Use Synology Docker GUI
echo 1. Open https://$SynologyHost`:$SynologyPort
echo 2. Go to Docker -^> Container
echo 3. Import the docker-compose.yml files
echo 4. Start containers one by one
echo.

pause
"@
    
    Set-Content (Join-Path $OutputDir "deploy-windows.bat") $windowsScript
    
    Write-Host "`n✅ Deployment package created in '$OutputDir'" -ForegroundColor Green
    Write-Host "📊 Packaged $successCount/$($mcpServers.Count) servers" -ForegroundColor Cyan
    
    return $successCount -eq $mcpServers.Count
}

function Show-DeploymentInstructions {
    Write-Host "`n📋 Synology NAS Deployment Instructions" -ForegroundColor Cyan
    Write-Host "=======================================" -ForegroundColor Cyan
    
    Write-Host "`n🎯 Option 1: SSH Deployment (Recommended)" -ForegroundColor Yellow
    Write-Host "1. Copy the '$OutputDir' folder to your Synology NAS" -ForegroundColor White
    Write-Host "   - Use File Station or SCP to copy to /volume1/docker/" -ForegroundColor Gray
    
    Write-Host "`n2. SSH into your Synology NAS:" -ForegroundColor White
    Write-Host "   ssh admin@$SynologyHost" -ForegroundColor Gray
    
    Write-Host "`n3. Navigate to deployment directory:" -ForegroundColor White
    Write-Host "   cd /volume1/docker/$OutputDir" -ForegroundColor Gray
    
    Write-Host "`n4. Make scripts executable and deploy:" -ForegroundColor White
    Write-Host "   chmod +x deploy-all.sh" -ForegroundColor Gray
    Write-Host "   ./deploy-all.sh" -ForegroundColor Gray
    
    Write-Host "`n🎯 Option 2: Synology Docker GUI" -ForegroundColor Yellow
    Write-Host "1. Open Synology Docker interface:" -ForegroundColor White
    Write-Host "   https://$SynologyHost`:$SynologyPort" -ForegroundColor Gray
    
    Write-Host "`n2. For each MCP server:" -ForegroundColor White
    Write-Host "   - Go to Container tab" -ForegroundColor Gray
    Write-Host "   - Click 'Create' -> 'Via docker-compose'" -ForegroundColor Gray
    Write-Host "   - Upload the docker-compose.yml file" -ForegroundColor Gray
    Write-Host "   - Start the container" -ForegroundColor Gray
    
    Write-Host "`n🎯 Option 3: Individual Server Deployment" -ForegroundColor Yellow
    Write-Host "Deploy servers one at a time for testing:" -ForegroundColor White
    
    foreach ($server in ($mcpServers | Sort-Object Priority)) {
        Write-Host "`n$($server.Priority). $($server.Name)" -ForegroundColor Cyan
        Write-Host "   cd $($server.Directory)" -ForegroundColor Gray
        Write-Host "   chmod +x deploy.sh && ./deploy.sh" -ForegroundColor Gray
        Write-Host "   Test: http://$SynologyHost`:$($server.Port)/health" -ForegroundColor Gray
    }
    
    Write-Host "`n🔍 Verification Steps:" -ForegroundColor Yellow
    Write-Host "1. Check containers are running:" -ForegroundColor White
    Write-Host "   docker ps --filter 'network=mcp-network'" -ForegroundColor Gray
    
    Write-Host "`n2. Test health endpoints:" -ForegroundColor White
    foreach ($server in $mcpServers) {
        Write-Host "   curl http://$SynologyHost`:$($server.Port)/health" -ForegroundColor Gray
    }
    
    Write-Host "`n3. View logs if needed:" -ForegroundColor White
    Write-Host "   docker logs [container-name]" -ForegroundColor Gray
    
    Write-Host "`n🎉 After Deployment:" -ForegroundColor Yellow
    Write-Host "- Your MCP servers will be accessible from your local machine" -ForegroundColor White
    Write-Host "- Update your VS Code settings to point to Synology ports" -ForegroundColor White
    Write-Host "- Test integration with GitHub Copilot" -ForegroundColor White
}

function Create-SynologyDockerComposeTemplate {
    $templateDir = Join-Path $OutputDir "templates"
    New-Item -ItemType Directory -Path $templateDir -Force | Out-Null
    
    # Create a master docker-compose.yml for all services
    $masterCompose = @"
version: '3.8'

services:
"@
    
    foreach ($server in $mcpServers) {
        $serviceName = $server.Directory -replace '-', '_'
        $masterCompose += @"

  $serviceName:
    build: ./$($server.Directory)
    container_name: $($server.Directory)
    ports:
      - "$($server.Port):$($server.Port)"
    environment:
      - OLLAMA_BASE_URL=http://$SynologyHost`:11434
    networks:
      - mcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:$($server.Port)/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
"@
    }
    
    $masterCompose += @"

networks:
  mcp-network:
    driver: bridge

volumes:
  mcp-data:
    driver: local
"@
    
    Set-Content (Join-Path $templateDir "docker-compose-all.yml") $masterCompose
    Write-Host "✅ Created master docker-compose template" -ForegroundColor Green
}

# Main execution
if ($CreatePackage) {
    $success = Create-SynologyDeploymentPackage
    Create-SynologyDockerComposeTemplate
    
    if ($success) {
        Write-Host "`n🎉 Deployment package created successfully!" -ForegroundColor Green
        Show-DeploymentInstructions
    } else {
        Write-Host "`n⚠️  Deployment package created with some issues" -ForegroundColor Yellow
        Show-DeploymentInstructions
    }
} elseif ($ShowInstructions) {
    Show-DeploymentInstructions
} else {
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  Create deployment package: -CreatePackage" -ForegroundColor White
    Write-Host "  Show instructions: -ShowInstructions" -ForegroundColor White
    Write-Host ""
    Write-Host "Example:" -ForegroundColor Yellow
    Write-Host "  .\deploy-to-synology.ps1 -CreatePackage" -ForegroundColor Gray
}
