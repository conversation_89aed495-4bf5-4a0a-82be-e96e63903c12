#!/usr/bin/env pwsh
# Test Synology NAS Connectivity

param (
    [string]$SynologyHost = "************"
)

Write-Host "Testing Synology NAS Connectivity" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan
Write-Host "Target: $SynologyHost" -ForegroundColor Yellow
Write-Host ""

# Test basic ping
Write-Host "1. Testing basic connectivity..." -NoNewline
try {
    $ping = Test-Connection -ComputerName $SynologyHost -Count 1 -Quiet
    if ($ping) {
        Write-Host " OK" -ForegroundColor Green
    } else {
        Write-Host " FAILED" -ForegroundColor Red
    }
} catch {
    Write-Host " FAILED" -ForegroundColor Red
}

# Test common Synology ports
$ports = @(
    @{ Port = 5000; Service = "Synology Web (HTTP)" },
    @{ Port = 5001; Service = "Synology Web (HTTPS)" },
    @{ Port = 7031; Service = "Docker Interface" },
    @{ Port = 22; Service = "SSH" },
    @{ Port = 11434; Service = "Ollama Server" }
)

Write-Host "`n2. Testing service ports:" -ForegroundColor Blue
foreach ($portTest in $ports) {
    Write-Host "   $($portTest.Service) (Port $($portTest.Port))..." -NoNewline
    try {
        $connection = Test-NetConnection -ComputerName $SynologyHost -Port $portTest.Port -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host " OK" -ForegroundColor Green
        } else {
            Write-Host " FAILED" -ForegroundColor Red
        }
    } catch {
        Write-Host " FAILED" -ForegroundColor Red
    }
}

# Test web interfaces
Write-Host "`n3. Testing web interfaces:" -ForegroundColor Blue

$webTests = @(
    "http://$SynologyHost`:5000",
    "https://$SynologyHost`:5001",
    "http://$SynologyHost`:7031",
    "https://$SynologyHost`:7031"
)

foreach ($url in $webTests) {
    Write-Host "   $url..." -NoNewline
    try {
        if ($url.StartsWith("https")) {
            $response = Invoke-WebRequest -Uri $url -TimeoutSec 5 -SkipCertificateCheck -ErrorAction SilentlyContinue
        } else {
            $response = Invoke-WebRequest -Uri $url -TimeoutSec 5 -ErrorAction SilentlyContinue
        }
        Write-Host " OK (Status: $($response.StatusCode))" -ForegroundColor Green
    } catch {
        Write-Host " FAILED" -ForegroundColor Red
    }
}

Write-Host "`n4. Recommendations:" -ForegroundColor Yellow

Write-Host "If all tests failed:" -ForegroundColor White
Write-Host "  - Check if Synology NAS is powered on" -ForegroundColor Gray
Write-Host "  - Verify IP address is correct" -ForegroundColor Gray
Write-Host "  - Check network connectivity" -ForegroundColor Gray

Write-Host "`nIf basic ping works but web interfaces fail:" -ForegroundColor White
Write-Host "  - Try accessing Synology via browser manually" -ForegroundColor Gray
Write-Host "  - Check if Docker package is installed" -ForegroundColor Gray
Write-Host "  - Verify firewall settings" -ForegroundColor Gray

Write-Host "`nIf Ollama works but Docker interface doesn't:" -ForegroundColor White
Write-Host "  - Install Docker package from Synology Package Center" -ForegroundColor Gray
Write-Host "  - Docker interface should then be available at port 7031" -ForegroundColor Gray

Write-Host "`nNext steps if connectivity works:" -ForegroundColor White
Write-Host "  1. Access Synology Docker interface" -ForegroundColor Gray
Write-Host "  2. Deploy MCP servers using docker-compose files" -ForegroundColor Gray
Write-Host "  3. Test individual servers one by one" -ForegroundColor Gray

Write-Host "`nConnectivity test completed!" -ForegroundColor Green
