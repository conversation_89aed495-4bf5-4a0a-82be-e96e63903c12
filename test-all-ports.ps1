#!/usr/bin/env pwsh
# Test Multiple Ports on Synology

param (
    [string]$SynologyHost = "************"
)

Write-Host "Testing Multiple Ports on Synology NAS" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host "Host: $SynologyHost" -ForegroundColor Yellow
Write-Host ""

# Test common MCP ports
$ports = @("3001", "3002", "3005", "3006", "3007", "3008", "3009", "3010", "3011", "3012", "3013", "3014", "3015", "8000", "8080", "8081")

Write-Host "Testing MCP ports:" -ForegroundColor Blue
foreach ($port in $ports) {
    Write-Host "Port $port..." -NoNewline
    try {
        $portTest = Test-NetConnection -ComputerName $SynologyHost -Port $port -WarningAction SilentlyContinue
        if ($portTest.TcpTestSucceeded) {
            Write-Host " OPEN" -ForegroundColor Green
            
            # Try to get HTTP response
            try {
                $response = Invoke-WebRequest -Uri "http://$SynologyHost`:$port/health" -TimeoutSec 3 -ErrorAction SilentlyContinue
                Write-Host "    HTTP /health: $($response.StatusCode) - $($response.Content)" -ForegroundColor Green
            } catch {
                try {
                    $response = Invoke-WebRequest -Uri "http://$SynologyHost`:$port/" -TimeoutSec 3 -ErrorAction SilentlyContinue
                    Write-Host "    HTTP /: $($response.StatusCode)" -ForegroundColor Yellow
                } catch {
                    Write-Host "    HTTP: No response" -ForegroundColor Gray
                }
            }
        } else {
            Write-Host " CLOSED" -ForegroundColor Red
        }
    } catch {
        Write-Host " ERROR" -ForegroundColor Red
    }
}

Write-Host "`nTesting Docker-related ports:" -ForegroundColor Blue
$dockerPorts = @("2375", "2376", "7031")
foreach ($port in $dockerPorts) {
    Write-Host "Docker port $port..." -NoNewline
    try {
        $portTest = Test-NetConnection -ComputerName $SynologyHost -Port $port -WarningAction SilentlyContinue
        if ($portTest.TcpTestSucceeded) {
            Write-Host " OPEN" -ForegroundColor Green
        } else {
            Write-Host " CLOSED" -ForegroundColor Red
        }
    } catch {
        Write-Host " ERROR" -ForegroundColor Red
    }
}

Write-Host "`nSummary:" -ForegroundColor Cyan
Write-Host "If no MCP ports are open, the containers are likely not running." -ForegroundColor Yellow
Write-Host "Please check container status via SSH:" -ForegroundColor White
Write-Host "  ssh swilliams@$SynologyHost" -ForegroundColor Gray
Write-Host "  docker ps -a" -ForegroundColor Gray
Write-Host "  docker logs markitdown-mcp-server" -ForegroundColor Gray
