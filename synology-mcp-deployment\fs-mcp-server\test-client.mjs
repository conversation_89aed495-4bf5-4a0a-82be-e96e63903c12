import { createRequire } from 'module';
const require = createRequire(import.meta.url);
const EventSource = require('eventsource');
global.EventSource = EventSource;

import { Client } from '@modelcontextprotocol/sdk/dist/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/dist/client/sse.js';

async function main() {
    const client = new Client({
        name: "test-client",
        version: "1.0.0"
    }, {
        capabilities: {}
    });

    console.log('Creating SSE transport...');
    const transport = new SSEClientTransport(
        new URL("http://localhost:3003/sse")
    );

    console.log('Connecting to server...');
    try {
        await client.connect(transport);
        console.log('Successfully connected to server');
        
        // Test directory listing
        const result = await client.invoke('listDirectory', {
            directoryPath: '.'
        });
        console.log('Directory listing result:', result);
        
    } catch (error) {
        console.error('Error connecting to server:', error);
        process.exit(1);
    } finally {
        try {
            await client.disconnect();
            console.log('Disconnected from server');
        } catch (error) {
            console.error('Error disconnecting:', error);
        }
    }
}

main().catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
});
