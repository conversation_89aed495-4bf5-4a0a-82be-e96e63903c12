#!/usr/bin/env python3
"""
Enhanced OpenHands Startup Script
Updated with Tavily search integration and optimizations
Fixed for port 3001
"""

import os
import sys
import time
import requests
from pathlib import Path

def print_banner():
    print("""
    ╔═══════════════════════════════════════════════════════════════╗
    ║                   🤖 All-Hands AI + Ollama                   ║
    ║                   Enhanced with Tavily Search                ║
    ║                     Running on Port 3001                     ║
    ╚═══════════════════════════════════════════════════════════════╝
    """)

def load_env_file(env_path):
    """Load environment variables from .env file"""
    if not os.path.exists(env_path):
        print(f"❌ Environment file not found: {env_path}")
        return False
    
    print(f"📋 Loading environment from: {env_path}")
    with open(env_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key.strip()] = value.strip()
    
    print("✅ Environment variables loaded")
    return True

def test_ollama_connection():
    """Test connection to Ollama server"""
    ollama_url = os.environ.get('LLM_BASE_URL', 'http://************:11434')
    print(f"🔍 Testing Ollama connection to: {ollama_url}")
    
    try:
        response = requests.get(f"{ollama_url}/api/tags", timeout=10)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama server accessible with {len(models)} models")
            
            configured_model = os.environ.get('LLM_MODEL', '').replace('ollama/', '')
            if configured_model:
                model_exists = any(m['name'].startswith(configured_model.split(':')[0]) for m in models)
                if model_exists:
                    print(f"✅ Model family '{configured_model}' is available")
                else:
                    print(f"⚠️  Model '{configured_model}' not found, available models:")
                    for model in models[:5]:
                        print(f"   - {model['name']}")
            return True
        else:
            print(f"❌ Ollama server returned status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to Ollama server: {e}")
        return False

def test_tavily_connection():
    """Test Tavily API key"""
    tavily_key = os.environ.get('TAVILY_API_KEY')
    if not tavily_key:
        print("⚠️  Tavily API key not found")
        return False
    
    print("🔍 Testing Tavily API connection...")
    
    try:
        # Simple test API call
        response = requests.post(
            "https://api.tavily.com/search",
            json={
                "api_key": tavily_key,
                "query": "test",
                "max_results": 1
            },
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ Tavily API key is valid and working")
            return True
        else:
            print(f"❌ Tavily API returned status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot test Tavily API: {e}")
        return False

def start_openhands():
    """Start OpenHands with enhanced configuration"""
    print("🚀 Starting All-Hands with enhanced configuration...")
    
    try:
        # Import OpenHands
        from openhands.server.listen import main as server_main
        
        # Set up additional environment variables for runtime stability
        os.environ.setdefault('PORT', '3001')
        os.environ.setdefault('HOST', '0.0.0.0')
        os.environ.setdefault('SANDBOX_TYPE', 'docker')
        os.environ.setdefault('SANDBOX_USER_ID', '1000')
        os.environ.setdefault('WORKSPACE_MOUNT_PATH', '/workspace')
        os.environ.setdefault('RUNTIME_STARTUP_TIMEOUT', '120')
        
        # Ensure workspace directory exists
        workspace_dir = os.path.join(os.getcwd(), 'workspaces')
        if not os.path.exists(workspace_dir):
            os.makedirs(workspace_dir, mode=0o755)
            print(f"✅ Created workspace directory: {workspace_dir}")
        
        print(f"🌐 Server starting on: http://localhost:{os.environ.get('PORT', '3001')}")
        print("📝 Web interface will be available shortly...")
        print("🔍 Search capabilities: Enabled (Tavily)")
        print("🦙 LLM Backend: Ollama")
        print("� Runtime: Docker sandbox")
        print("�🛑 Press Ctrl+C to stop")
        print()
        print("⏳ If you see 'Waiting for runtime to start...', please wait up to 2 minutes")
        print("   The runtime container needs time to initialize on first start")
        print()
        
        # Start the server
        server_main()
        
    except ImportError as e:
        print(f"❌ Cannot import OpenHands: {e}")
        print("💡 Install with: pip install openhands-ai --upgrade")
        return False
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        print("\n🔧 If you're seeing runtime issues, try:")
        print("   1. Run: python fix_runtime.py")
        print("   2. Check Docker Desktop is running")
        print("   3. Restart Docker service")
        return False

def main():
    """Main startup function"""
    print_banner()
    
    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Load configuration
    if not load_env_file("configs/.env"):
        return 1
    
    # Test connections
    ollama_ok = test_ollama_connection()
    tavily_ok = test_tavily_connection()
    
    print()
    if not ollama_ok:
        print("⚠️  Ollama connection failed - please check your Ollama server")
    
    if not tavily_ok:
        print("⚠️  Tavily connection failed - search features may not work")
    
    if not ollama_ok and not tavily_ok:
        print("❌ Critical services unavailable - please check configuration")
        return 1
    
    print("🎯 Starting All-Hands on port 3001...")
    time.sleep(2)
    
    try:
        start_openhands()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        return 0
    except Exception as e:
        print(f"\n❌ Startup error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
