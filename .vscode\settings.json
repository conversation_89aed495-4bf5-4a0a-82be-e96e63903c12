{
  "mcp.servers": {
    "markitdown-mcp": {
      "type": "stdio",
      "name": "MarkItDown MCP Server",
      "description": "Document conversion with OCR support",
      "command": "docker",
      "args": [
        "run",
        "--rm",
        "-i",
        "markitdown-mcp:latest"
      ],
      "enabled": true,
      "capabilities": [
        "document-conversion",
        "pdf-to-markdown",
        "ocr-processing",
        "text-extraction"
      ]
    },
    "tavily-search": {
      "type": "http",
      "name": "Tavily Search MCP Server",
      "description": "AI-powered web search and research",
      "endpoint": "http://localhost:8000",
      "enabled": true,
      "capabilities": [
        "web-search",
        "research",
        "real-time-data"
      ]
    },
    "bing-search": {
      "type": "stdio",
      "name": "Bing Search MCP Server",
      "description": "Microsoft Bing web search",
      "command": "docker",
      "args": [
        "exec",
        "bing-mcp-server",
        "python",
        "-m",
        "mcp_server_bing"
      ],
      "enabled": true,
      "capabilities": [
        "web-search",
        "bing-api",
        "search-results"
      ]
    },
    "duckduckgo-search": {
      "type": "stdio",
      "name": "DuckDuckGo Search MCP Server",
      "description": "Privacy-focused web search",
      "command": "docker",
      "args": [
        "exec",
        "-i",
        "duckduckgo-search-mcp-server",
        "python",
        "main.py"
      ],
      "enabled": true,
      "capabilities": [
        "web-search",
        "privacy-search",
        "no-tracking"
      ]
    },
    "filesystem": {
      "type": "stdio",
      "name": "Filesystem MCP Server",
      "description": "File system operations and management",
      "command": "docker",
      "args": [
        "exec",
        "-i",
        "fs-mcp-final",
        "/app/docker-entrypoint.sh",
        "stdio",
        "/app/workspace"
      ],
      "enabled": true,
      "capabilities": [
        "file-operations",
        "directory-management",
        "file-search"
      ]
    },
    "magic-ui": {
      "type": "stdio",
      "name": "Magic UI Component Generator",
      "description": "AI-powered UI component generation like v0",
      "command": "docker",
      "args": [
        "exec",
        "-i",
        "magic-mcp-server",
        "/app/docker-entrypoint.sh",
        "stdio"
      ],
      "enabled": true,
      "capabilities": [
        "ui-generation",
        "component-creation",
        "react-components",
        "typescript-support"
      ]
    },
    "excel-data": {
      "type": "stdio",
      "name": "Excel Data Manipulation",
      "description": "Read and write MS Excel files with advanced features",
      "command": "docker",
      "args": [
        "exec",
        "-i",
        "excel-mcp-server",
        "/app/docker-entrypoint.sh",
        "stdio"
      ],
      "enabled": true,
      "capabilities": [
        "excel-read",
        "excel-write",
        "excel-formulas",
        "excel-tables",
        "sheet-management"
      ]
    },
    "outlook-mcp": {
      "type": "stdio",
      "name": "Outlook MCP Server",
      "description": "Microsoft Outlook integration for emails, calendar, and contacts",
      "command": "docker",
      "args": [
        "run",
        "--rm",
        "-i",
        "-v",
        "outlook-mcp-tokens:/app/tokens",
        "-e",
        "OUTLOOK_CLIENT_ID=77f98e9e-50af-4e4d-af6b-dce28bd531fc",
        "-e",
        "USE_TEST_MODE=false",
        "outlook-mcp-ryaker:latest"
      ],
      "enabled": true,
      "capabilities": [
        "email-management",
        "calendar-integration",
        "contact-management",
        "folder-operations",
        "search-functionality",
        "authentication"
      ]
    },
    "weather-streaming": {
      "type": "stdio",
      "name": "Weather Streaming MCP Server",
      "description": "Weather data and forecasts with streaming updates",
      "command": "docker",
      "args": [
        "run",
        "--rm",
        "-i",
        "weather-streaming-mcp:latest"
      ],
      "enabled": true,
      "capabilities": [
        "weather-data",
        "forecasting",
        "streaming-updates"
      ]
    },
    "email-streaming": {
      "type": "stdio",
      "name": "Email Streaming MCP Server",
      "description": "Email streaming and notifications",
      "command": "docker",
      "args": [
        "run",
        "--rm",
        "-i",
        "email-streaming-mcp:latest"
      ],
      "enabled": true,
      "capabilities": [
        "email-streaming",
        "notifications",
        "real-time-monitoring"
      ]
    },
    "system-monitoring": {
      "type": "stdio",
      "name": "System Monitoring MCP Server",
      "description": "System resource monitoring and metrics",
      "command": "docker",
      "args": [
        "run",
        "--rm",
        "-i",
        "system-monitoring-mcp:latest"
      ],
      "enabled": true,
      "capabilities": [
        "system-metrics",
        "resource-monitoring",
        "performance-analysis"
      ]
    },
    "strands-agents": {
      "type": "stdio",
      "name": "Strands Agents MCP Server",
      "description": "Advanced agent capabilities with multiple skills",
      "command": "docker",
      "args": [
        "exec",
        "-i",
        "strands-agents-mcp-server",
        "python",
        "-m",
        "strands_mcp_server"
      ],
      "enabled": true,
      "capabilities": [
        "agent-management",
        "skill-coordination",
        "autonomous-tasks"
      ]
    },
    "playwright": {
      "type": "stdio",
      "name": "Playwright MCP Server",
      "description": "Browser automation and web testing capabilities",
      "command": "docker",
      "args": [
        "run",
        "--rm",
        "-i",
        "mcr.microsoft.com/playwright/mcp:latest"
      ],
      "enabled": true,
      "capabilities": [
        "browser-automation",
        "web-testing",
        "screenshot-capture",
        "page-interaction"
      ]
    },
    "context7": {
      "port": 3000,
      "globalAccess": true,
      "enabled": true,
      "contextManagement": {
        "crossExtension": true,
        "autoSave": true,
        "persistContext": true
      }
    }
  },
  "nas-global-mcp": {
    "type": "http",
    "name": "NAS Global MCP Server",
    "description": "Global MCP server running on Synology NAS",
    "url": "http://************:7031",
    "enabled": true,
    "globalAccess": true,
    "capabilities": [
      "global-context",
      "cross-extension",
      "persistent-data"
    ]
  }
  },
  "mcp.serversList": [
    "markitdown-mcp",
    "tavily-search",
    "bing-search",
    "duckduckgo-search",
    "filesystem",
    "magic-ui",
    "excel-data",
    "outlook-mcp",
    "weather-streaming",
    "email-streaming",
    "system-monitoring",
    "strands-agents",
    "playwright",
    "context7",
    "nas-global-mcp"
  ],
  "mcp.enabled": true,
  "mcp.autoStart": true,
  "mcp.debug": false,
  "mcp.timeout": 30000,
  "github.copilot.chat.useMcp": true,
  "github.copilot.inlineChat.useMcp": true,
  "github.copilot.chat.features.mcp": true,
  "github.copilot.chat.features.tools": true,
  "github.copilot.chat.tools": [
    "markitdown-mcp",
    "magic-ui",
    "excel-data",
    "outlook-mcp"
  ],
  "github.copilot.mcpServers": [
    "markitdown-mcp",
    "tavily-search",
    "bing-search",
    "duckduckgo-search",
    "filesystem",
    "magic-ui",
    "excel-data",
    "outlook-mcp",
    "weather-streaming",
    "email-streaming",
    "system-monitoring",
    "strands-agents",
    "playwright",
    "context7"
  ],
  "continue.mcpConfigFile": "C:\\Users\\<USER>\\.continue\\config.json",
  "claude-dev.mcpServers": [
    "markitdown-mcp",
    "tavily-search",
    "bing-search",
    "duckduckgo-search",
    "filesystem",
    "magic-ui",
    "excel-data",
    "outlook-mcp"
  ],
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "Tavily Search",
        "command": "docker",
        "args": [
          "exec",
          "-i",
          "tavily-mcp-server",
          "python",
          "-m",
          "mcp_server_tavily"
        ]
      },
      {
        "name": "DuckDuckGo Search",
        "command": "docker",
        "args": [
          "exec",
          "-i",
          "duckduckgo-search-mcp-server",
          "python",
          "main.py"
        ]
      },
      {
        "name": "MarkItDown",
        "command": "docker",
        "args": [
          "run",
          "--rm",
          "-i",
          "markitdown-mcp:latest"
        ]
      },
      {
        "name": "Outlook MCP",
        "command": "docker",
        "args": [
          "run",
          "--rm",
          "-i",
          "-v",
          "outlook-mcp-tokens:/app/tokens",
          "-e",
          "OUTLOOK_CLIENT_ID=77f98e9e-50af-4e4d-af6b-dce28bd531fc",
          "-e",
          "USE_TEST_MODE=false",
          "outlook-mcp-ryaker:latest"
        ]
      }
    ]
  },
  "markitdown.enabled": true,
  "markitdown.mode": "standalone",
  "markitdown.ocr.enabled": true,
  "markitdown.docker.enabled": false,
  "search.tavily.enabled": true,
  "search.bing.enabled": true,
  "search.duckduckgo.enabled": true,
  "search.duckduckgo.privacy": true,
  "docker.mcp.enabled": true,
  "docker.mcp.containers": [
    "markitdown-mcp-server",
    "tavily-mcp-server",
    "bing-mcp-server",
    "duckduckgo-search-mcp-server",
    "fs-mcp-final",
    "magic-mcp-server",
    "excel-mcp-server",
    "outlook-mcp-server"
  ],
  "mcp.globalSecrets.enabled": true,
  "mcp.globalSecrets.volume": "mcp-global-secrets",
  "mcp.globalSecrets.envFile": "C:\\Users\\<USER>\\.mcp-secrets\\global.env",
  "outlook.mcp.enabled": true,
  "outlook.mcp.clientId": "77f98e9e-50af-4e4d-af6b-dce28bd531fc",
  "outlook.mcp.tenantId": "6bf1c2d8-9d93-4111-9b87-5e069f440055",
  "outlook.mcp.authentication.enabled": true,
  "outlook.mcp.features.email": true,
  "outlook.mcp.features.calendar": true,
  "outlook.mcp.features.contacts": true,
  "outlook.mcp.features.folders": true,
  "workspace.mcp.autoDiscovery": true,
  "workspace.mcp.configFile": "${workspaceFolder}/.mcp-config.json",
  "extensions.experimental.mcpIntegration": true,
  "context7.mcp.enabled": true,
  "context7.mcp.globalAccess": true
}
