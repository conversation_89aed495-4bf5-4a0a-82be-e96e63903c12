# Simple SSH Test for NAS MCP Connection
Write-Host "Testing SSH connection to Synology NAS..." -ForegroundColor Green

# Test basic connectivity
Write-Host "1. Testing port 22 connectivity..." -ForegroundColor Yellow
$connection = Test-NetConnection -ComputerName "************" -Port 22 -WarningAction SilentlyContinue
if ($connection.TcpTestSucceeded) {
    Write-Host "   ✓ SSH port 22 is accessible" -ForegroundColor Green
} else {
    Write-Host "   ✗ SSH port 22 is not accessible" -ForegroundColor Red
}

# Test MCP port
Write-Host "2. Testing port 7031 connectivity..." -ForegroundColor Yellow
$mcpConnection = Test-NetConnection -ComputerName "************" -Port 7031 -WarningAction SilentlyContinue
if ($mcpConnection.TcpTestSucceeded) {
    Write-Host "   ✓ MCP port 7031 is accessible" -ForegroundColor Green
} else {
    Write-Host "   ✗ MCP port 7031 is not accessible" -ForegroundColor Red
}

Write-Host "`n3. Manual SSH connection command:" -ForegroundColor Yellow
Write-Host "   ssh swilliams@************" -ForegroundColor Cyan

Write-Host "`n4. Check Docker containers on NAS:" -ForegroundColor Yellow
Write-Host "   ssh swilliams@************ 'docker ps | grep mcp'" -ForegroundColor Cyan

Write-Host "`n5. Start MCP container if needed:" -ForegroundColor Yellow
Write-Host "   ssh swilliams@************ 'docker run -d -p 7031:7031 --name global-mcp-server mcp-image:latest'" -ForegroundColor Cyan
