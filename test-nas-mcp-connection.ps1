# Test NAS MCP Server Connection
param(
    [string]$NasUrl = "http://************:7031",
    [int]$TimeoutSeconds = 30
)

Write-Host "Testing connection to NAS MCP Server at $NasUrl" -ForegroundColor Green

# Test basic connectivity
try {
    Write-Host "1. Testing basic HTTP connectivity..." -ForegroundColor Yellow
    $response = Invoke-WebRequest -Uri "$NasUrl/health" -TimeoutSec $TimeoutSeconds -ErrorAction Stop
    Write-Host "   ✓ HTTP connection successful (Status: $($response.StatusCode))" -ForegroundColor Green
}
catch {
    Write-Host "   ✗ HTTP connection failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Trying alternative endpoints..." -ForegroundColor Yellow
}

# Test MCP protocol endpoints
$mcpEndpoints = @(
    "/mcp/v1/initialize",
    "/mcp/v1/tools/list",
    "/mcp/v1/resources/list"
)

foreach ($endpoint in $mcpEndpoints) {
    try {
        Write-Host "2. Testing MCP endpoint: $endpoint" -ForegroundColor Yellow
        $response = Invoke-WebRequest -Uri "$NasUrl$endpoint" -TimeoutSec $TimeoutSeconds -ErrorAction Stop
        Write-Host "   ✓ Endpoint accessible (Status: $($response.StatusCode))" -ForegroundColor Green
    }
    catch {
        Write-Host "   ✗ Endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test port accessibility
Write-Host "3. Testing port accessibility..." -ForegroundColor Yellow
try {
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $tcpClient.ConnectAsync("************", 7031).Wait($TimeoutSeconds * 1000)
    if ($tcpClient.Connected) {
        Write-Host "   ✓ Port 7031 is accessible" -ForegroundColor Green
        $tcpClient.Close()
    } else {
        Write-Host "   ✗ Port 7031 is not accessible" -ForegroundColor Red
    }
}
catch {
    Write-Host "   ✗ Port test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nConnection test completed." -ForegroundColor Green
Write-Host "If all tests pass, your NAS MCP server should work with VS Code." -ForegroundColor Cyan
