version: '3.8'

services:
  fs-mcp-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: fs-mcp-final
    ports:
      - "3002:3002"
    volumes:
      - C:/Users/<USER>/vsproject:/app/workspace
      - C:/Users/<USER>/app/home
    environment:
      - PORT=3002
      - HOST=0.0.0.0
      - NODE_ENV=production
    command: ["/app/workspace", "/app/home"]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge
