#!/usr/bin/env pwsh
# Synology MCP Diagnostic Script
# Analyzes current MCP setup and provides recommendations for Synology integration

param (
    [Parameter()]
    [string]$SynologyHost = "************",
    
    [Parameter()]
    [string]$SynologyPort = "7031"
)

Write-Host "🔍 Synology MCP Diagnostic Report" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan
Write-Host "Target Synology: https://${SynologyHost}:${SynologyPort}" -ForegroundColor Yellow
Write-Host ""

# Function to test connectivity
function Test-Connectivity {
    param (
        [string]$TargetHost,
        [string]$Port,
        [string]$Protocol = "http"
    )
    
    try {
        $uri = "$Protocol`://$TargetHost`:$Port"
        if ($Protocol -eq "https") {
            $response = Invoke-WebRequest -Uri $uri -TimeoutSec 5 -SkipCertificateCheck -ErrorAction SilentlyContinue
        }
        else {
            $response = Invoke-WebRequest -Uri $uri -TimeoutSec 5 -ErrorAction SilentlyContinue
        }
        return @{ Success = $true; StatusCode = $response.StatusCode }
    }
    catch {
        return @{ Success = $false; Error = $_.Exception.Message }
    }
}

# Function to check Docker status
function Test-DockerEnvironment {
    Write-Host "🐳 Docker Environment Check" -ForegroundColor Blue
    Write-Host "----------------------------" -ForegroundColor Blue
    
    try {
        $dockerVersion = docker version --format "{{.Server.Version}}" 2>$null
        if ($dockerVersion) {
            Write-Host "✅ Docker Server: $dockerVersion" -ForegroundColor Green
        }
        else {
            Write-Host "❌ Docker Server: Not accessible" -ForegroundColor Red
            return $false
        }
        
        $dockerClient = docker version --format "{{.Client.Version}}" 2>$null
        if ($dockerClient) {
            Write-Host "✅ Docker Client: $dockerClient" -ForegroundColor Green
        }
        
        # Check Docker Compose
        $composeVersion = docker-compose version --short 2>$null
        if ($composeVersion) {
            Write-Host "✅ Docker Compose: $composeVersion" -ForegroundColor Green
        }
        else {
            Write-Host "⚠️  Docker Compose: Not found" -ForegroundColor Yellow
        }
        
        return $true
    }
    catch {
        Write-Host "❌ Docker: Not running or not accessible" -ForegroundColor Red
        return $false
    }
}

# Function to check existing MCP containers
function Get-ExistingContainers {
    Write-Host "`n📦 Existing MCP Containers" -ForegroundColor Blue
    Write-Host "---------------------------" -ForegroundColor Blue
    
    $mcpContainers = docker ps -a --filter "name=mcp" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>$null
    
    if ($mcpContainers) {
        Write-Host $mcpContainers
        
        # Count running vs stopped
        $allContainers = docker ps -a --filter "name=mcp" --format "{{.Names}}" 2>$null
        $runningContainers = docker ps --filter "name=mcp" --format "{{.Names}}" 2>$null
        
        if ($allContainers) {
            $totalCount = ($allContainers -split "`n").Count
            $runningCount = if ($runningContainers) { ($runningContainers -split "`n").Count } else { 0 }
            Write-Host "`n📊 Container Summary: $runningCount/$totalCount running" -ForegroundColor Cyan
        }
    }
    else {
        Write-Host "No MCP containers found" -ForegroundColor Yellow
    }
}

# Function to check network connectivity
function Test-NetworkConnectivity {
    Write-Host "`n🌐 Network Connectivity Check" -ForegroundColor Blue
    Write-Host "------------------------------" -ForegroundColor Blue
    
    # Test Synology NAS
    Write-Host "Testing Synology NAS..." -NoNewline
    $synologyTest = Test-Connectivity -TargetHost $SynologyHost -Port $SynologyPort -Protocol "https"
    if ($synologyTest.Success) {
        Write-Host " ✅ Accessible" -ForegroundColor Green
    }
    else {
        Write-Host " ❌ Failed" -ForegroundColor Red
        Write-Host "   Error: $($synologyTest.Error)" -ForegroundColor Gray
    }
    
    # Test Ollama
    Write-Host "Testing Ollama Server..." -NoNewline
    $ollamaTest = Test-Connectivity -TargetHost $SynologyHost -Port "11434" -Protocol "http"
    if ($ollamaTest.Success) {
        Write-Host " ✅ Accessible" -ForegroundColor Green
    }
    else {
        Write-Host " ❌ Failed" -ForegroundColor Red
        Write-Host "   Error: $($ollamaTest.Error)" -ForegroundColor Gray
    }
    
    # Test local MCP ports
    $mcpPorts = @("3001", "3002", "3005", "3006", "3007", "3008", "3009", "3010", "3011", "3012", "3013", "3014", "3015")
    $activePorts = @()
    
    foreach ($port in $mcpPorts) {
        $portTest = Test-Connectivity -TargetHost "localhost" -Port $port -Protocol "http"
        if ($portTest.Success) {
            $activePorts += $port
        }
    }
    
    if ($activePorts.Count -gt 0) {
        Write-Host "✅ Active MCP ports: $($activePorts -join ', ')" -ForegroundColor Green
    }
    else {
        Write-Host "⚠️  No active MCP ports found" -ForegroundColor Yellow
    }
}

# Function to check MCP directories
function Test-MCPDirectories {
    Write-Host "`n📁 MCP Server Directories" -ForegroundColor Blue
    Write-Host "--------------------------" -ForegroundColor Blue
    
    $mcpDirs = @(
        "markitdown-mcp-server",
        "outlook-mcp-server", 
        "google-ai-edge-mcp-server",
        "dji-mcp-server",
        "elevenlabs-integration",
        "fs-mcp-server",
        "openmanus-mcp-server",
        "strands-agents-mcp-server",
        "ollama-streaming-mcp",
        "weather-streaming-mcp",
        "email-streaming-mcp",
        "system-monitoring-mcp"
    )
    
    $foundDirs = @()
    $missingDirs = @()
    
    foreach ($dir in $mcpDirs) {
        if (Test-Path $dir) {
            $foundDirs += $dir
            $composeFile = Join-Path $dir "docker-compose.yml"
            $hasCompose = Test-Path $composeFile
            $composeStatus = if ($hasCompose) { "✅" } else { "❌" }
            Write-Host "$dir".PadRight(30) "$composeStatus docker-compose.yml" -ForegroundColor $(if ($hasCompose) { "Green" } else { "Yellow" })
        }
        else {
            $missingDirs += $dir
        }
    }
    
    Write-Host "`n📊 Directory Summary:" -ForegroundColor Cyan
    Write-Host "   Found: $($foundDirs.Count)" -ForegroundColor Green
    Write-Host "   Missing: $($missingDirs.Count)" -ForegroundColor $(if ($missingDirs.Count -gt 0) { "Red" } else { "Green" })
    
    if ($missingDirs.Count -gt 0) {
        Write-Host "`n❌ Missing directories:" -ForegroundColor Red
        foreach ($dir in $missingDirs) {
            Write-Host "   $dir" -ForegroundColor Gray
        }
    }
}

# Function to check VS Code configuration
function Test-VSCodeConfig {
    Write-Host "`n⚙️  VS Code MCP Configuration" -ForegroundColor Blue
    Write-Host "------------------------------" -ForegroundColor Blue
    
    $settingsPath = ".vscode\settings.json"
    if (Test-Path $settingsPath) {
        try {
            $settings = Get-Content -Raw $settingsPath | ConvertFrom-Json
            
            # Check GitHub Copilot MCP settings
            $mcpEnabled = $settings.'github.copilot.chat.useMcp'
            $mcpServers = $settings.'github.copilot.mcpServers'
            
            Write-Host "MCP Integration: $(if ($mcpEnabled) { '✅ Enabled' } else { '❌ Disabled' })" -ForegroundColor $(if ($mcpEnabled) { "Green" } else { "Red" })
            
            if ($mcpServers) {
                Write-Host "Configured servers: $($mcpServers.Count)" -ForegroundColor Green
                foreach ($server in $mcpServers) {
                    Write-Host "   - $server" -ForegroundColor Gray
                }
            }
            else {
                Write-Host "❌ No MCP servers configured" -ForegroundColor Red
            }
            
        }
        catch {
            Write-Host "❌ Error reading VS Code settings: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    else {
        Write-Host "❌ VS Code settings file not found" -ForegroundColor Red
    }
}

# Function to generate recommendations
function Get-Recommendations {
    Write-Host "`n💡 Recommendations" -ForegroundColor Blue
    Write-Host "------------------" -ForegroundColor Blue
    
    $recommendations = @()
    
    # Check if Docker is running
    if (-not (Test-DockerEnvironment)) {
        $recommendations += "🔧 Start Docker Desktop before proceeding"
    }
    
    # Check Synology connectivity
    $synologyTest = Test-Connectivity -TargetHost $SynologyHost -Port $SynologyPort -Protocol "https"
    if (-not $synologyTest.Success) {
        $recommendations += "🌐 Verify Synology NAS is accessible at https://${SynologyHost}:${SynologyPort}"
        $recommendations += "🔍 Check network connectivity and firewall settings"
    }
    
    # Check Ollama connectivity
    $ollamaTest = Test-Connectivity -TargetHost $SynologyHost -Port "11434" -Protocol "http"
    if (-not $ollamaTest.Success) {
        $recommendations += "🤖 Ensure Ollama is running on Synology NAS at port 11434"
    }
    
    # Check for running containers
    $runningContainers = docker ps --filter "name=mcp" --format "{{.Names}}" 2>$null
    if (-not $runningContainers) {
        $recommendations += "🚀 No MCP containers running - use reset script to start them"
    }
    
    # Check VS Code configuration
    $settingsPath = ".vscode\settings.json"
    if (-not (Test-Path $settingsPath)) {
        $recommendations += "⚙️  Configure VS Code settings for MCP integration"
    }
    
    if ($recommendations.Count -eq 0) {
        Write-Host "✅ No issues found! Your setup looks good." -ForegroundColor Green
    }
    else {
        foreach ($rec in $recommendations) {
            Write-Host $rec -ForegroundColor Yellow
        }
    }
}

# Function to show next steps
function Show-NextSteps {
    Write-Host "`n🎯 Next Steps" -ForegroundColor Blue
    Write-Host "-------------" -ForegroundColor Blue
    
    Write-Host "1. Run the reset script:" -ForegroundColor White
    Write-Host "   .\reset-synology-mcp-servers.ps1 -Force" -ForegroundColor Gray
    
    Write-Host "`n2. Manage your servers:" -ForegroundColor White
    Write-Host "   .\synology-mcp-manager.ps1 status" -ForegroundColor Gray
    Write-Host "   .\synology-mcp-manager.ps1 start -All" -ForegroundColor Gray
    
    Write-Host "`n3. Deploy to Synology (if needed):" -ForegroundColor White
    Write-Host "   .\synology-mcp-manager.ps1 deploy" -ForegroundColor Gray
    
    Write-Host "`n4. Monitor health:" -ForegroundColor White
    Write-Host "   .\synology-mcp-manager.ps1 health" -ForegroundColor Gray
}

# Main execution
Write-Host "Starting diagnostic scan..." -ForegroundColor White
Write-Host ""

# Run all checks
$dockerOk = Test-DockerEnvironment
Get-ExistingContainers
Test-NetworkConnectivity
Test-MCPDirectories
Test-VSCodeConfig

# Generate recommendations and next steps
Get-Recommendations
Show-NextSteps

Write-Host "`n✅ Diagnostic completed!" -ForegroundColor Green
Write-Host "📋 Review the recommendations above and run the suggested commands." -ForegroundColor White
