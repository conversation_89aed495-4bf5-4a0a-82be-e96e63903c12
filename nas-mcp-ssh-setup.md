# SSH Connection to Synology NAS for MCP Server

## Connection Details
- **Host**: `************`
- **Username**: `swilliams`
- **Password**: `Flight99!`
- **Port**: `22` (default SSH)

## Quick SSH Connection
```bash
ssh swilliams@************
```

## SSH-based MCP Server Configuration Options

### Option 1: SSH Tunnel for MCP Server
Create an SSH tunnel to securely access the MCP server:
```bash
# Forward local port 7031 to NAS port 7031
ssh -L 7031:localhost:7031 swilliams@************

# Then configure VS Code to use localhost:7031
```

### Option 2: Direct SSH Command Execution
Configure VS Code to execute MCP commands via SSH:

```json
{
  "mcp": {
    "servers": {
      "nas-ssh-mcp": {
        "type": "stdio",
        "command": "ssh",
        "args": [
          "swilliams@************",
          "docker", "exec", "-i", "mcp-server-container",
          "/app/mcp-server", "--stdio"
        ]
      }
    }
  }
}
```

### Option 3: SSH with Docker Exec
```json
{
  "mcp": {
    "servers": {
      "nas-docker-mcp": {
        "type": "stdio",
        "command": "ssh",
        "args": [
          "-o", "StrictHostKeyChecking=no",
          "swilliams@************",
          "docker run --rm -i mcp-server:latest"
        ]
      }
    }
  }
}
```

## SSH Key Setup (Recommended)
For passwordless authentication:

```bash
# Generate SSH key pair
ssh-keygen -t rsa -b 4096 -f ~/.ssh/nas_mcp_key

# Copy public key to NAS
ssh-copy-id -i ~/.ssh/nas_mcp_key.pub swilliams@************

# Use key in VS Code configuration
```

## Docker Container Management via SSH

### Check Running Containers
```bash
ssh swilliams@************ "docker ps | grep mcp"
```

### Start MCP Container
```bash
ssh swilliams@************ "docker run -d -p 7031:7031 --name global-mcp-server mcp-image:latest"
```

### View Container Logs
```bash
ssh swilliams@************ "docker logs global-mcp-server"
```

### Execute Commands in Container
```bash
ssh swilliams@************ "docker exec -it global-mcp-server /bin/bash"
```

## Security Considerations
- Use SSH keys instead of passwords
- Restrict SSH access to specific IP ranges
- Use SSH tunneling for encrypted communication
- Implement proper firewall rules on NAS
- Regular security updates on both client and NAS
