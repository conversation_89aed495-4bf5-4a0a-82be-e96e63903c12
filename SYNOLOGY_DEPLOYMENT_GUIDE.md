# Synology NAS MCP Server Deployment Guide

## 🎯 **Current Status**

Based on our diagnostic, here's what we found:

### ✅ **Working:**
- **Docker**: Running version 28.1.1
- **Ollama Server**: Accessible at `************:11434`
- **MCP Directories**: All 12 server directories found
- **VS Code Integration**: MCP enabled with 14 configured servers

### ⚠️ **Issues:**
- **Synology NAS**: Connection failed to `https://************:7031`
- **MCP Containers**: No containers currently running on Synology
- **Local vs Remote**: MCP servers need to run on Synology, not locally

## 🚀 **Solution: Deploy to Synology NAS**

Since your Synology NAS connection is not accessible from your local machine, you have several options:

### **Option 1: Direct Synology Docker GUI (Recommended)**

1. **Access Synology Docker Interface:**
   - Open your browser and go to: `https://************:7031`
   - Login with your Synology credentials
   - Navigate to **Docker** → **Container**

2. **Deploy MCP Servers One by One:**

   **Start with MarkItDown MCP (Port 3001):**
   - Click **"Create"** → **"Via docker-compose"**
   - Copy and paste this docker-compose.yml:

   ```yaml
   version: '3.8'
   
   services:
     markitdown-mcp-server:
       build: .
       container_name: markitdown-mcp-server
       ports:
         - "3001:3001"
       environment:
         - PYTHONUNBUFFERED=1
         - OLLAMA_BASE_URL=http://************:11434
       volumes:
         - ./workspace:/app/workspace
       networks:
         - mcp-network
       restart: unless-stopped
       healthcheck:
         test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
         interval: 30s
         timeout: 10s
         retries: 3
   
   networks:
     mcp-network:
       driver: bridge
   ```

   **Then add Filesystem MCP (Port 3012):**
   ```yaml
   version: '3.8'
   
   services:
     fs-mcp-server:
       build: .
       container_name: fs-mcp-final
       ports:
         - "3012:3012"
       environment:
         - PORT=3012
         - HOST=0.0.0.0
         - NODE_ENV=production
       volumes:
         - /volume1/homes:/app/workspace
       networks:
         - mcp-network
       restart: unless-stopped
   
   networks:
     mcp-network:
       external: true
   ```

### **Option 2: SSH Deployment**

If you can SSH to your Synology:

1. **SSH to Synology:**
   ```bash
   ssh admin@************
   ```

2. **Create deployment directory:**
   ```bash
   mkdir -p /volume1/docker/mcp-servers
   cd /volume1/docker/mcp-servers
   ```

3. **Create and deploy MarkItDown MCP:**
   ```bash
   mkdir markitdown-mcp
   cd markitdown-mcp
   
   # Create docker-compose.yml (copy content from Option 1)
   nano docker-compose.yml
   
   # Deploy
   docker-compose up -d
   
   # Test
   curl http://localhost:3001/health
   ```

### **Option 3: File Transfer + Docker Commands**

1. **Transfer files to Synology:**
   - Use Synology File Station to upload the `synology-mcp-deployment` folder
   - Place it in `/volume1/docker/`

2. **SSH and deploy:**
   ```bash
   ssh admin@************
   cd /volume1/docker/synology-mcp-deployment
   
   # Deploy each server
   cd markitdown-mcp-server
   docker-compose up -d
   cd ../fs-mcp-server
   docker-compose up -d
   ```

## 🔧 **Individual Server Testing Approach**

Since you want to test each MCP server individually, here's the recommended order:

### **1. MarkItDown MCP (Priority 1)**
- **Purpose**: Document conversion with OCR
- **Port**: 3001
- **Test**: `curl http://************:3001/health`
- **Why first**: Simple, reliable, good for testing basic setup

### **2. Filesystem MCP (Priority 2)**
- **Purpose**: File system operations
- **Port**: 3012
- **Test**: `curl http://************:3012/health`
- **Why second**: Essential for file operations, widely used

### **3. Outlook MCP (Priority 3)**
- **Purpose**: Email integration
- **Port**: 3002
- **Test**: `curl http://************:3002/health`
- **Why third**: Requires authentication setup

### **4. Google AI Edge MCP (Priority 4)**
- **Purpose**: AI processing
- **Port**: 3005
- **Test**: `curl http://************:3005/health`

### **5. OpenManus MCP (Priority 5)**
- **Purpose**: FoundationAgents integration
- **Port**: 3013
- **Test**: `curl http://************:3013/health`

## 🔍 **Troubleshooting Connection Issues**

If you can't access `https://************:7031`:

### **Check Network Connectivity:**
```powershell
# Test basic connectivity
ping ************

# Test specific port
Test-NetConnection -ComputerName ************ -Port 7031
```

### **Alternative Ports to Try:**
- **HTTP**: `http://************:5000` (default Synology web interface)
- **HTTPS**: `https://************:5001` (default HTTPS)
- **Docker**: `http://************:7031` (if Docker package is installed)

### **Synology Docker Package:**
1. Open Synology Package Center
2. Install **Docker** package if not already installed
3. Docker interface should be available at port 7031

## 🎯 **Next Steps**

1. **Verify Synology Access:**
   - Try accessing `http://************:5000` or `https://************:5001`
   - Ensure Docker package is installed

2. **Start with One Server:**
   - Deploy MarkItDown MCP first using Synology Docker GUI
   - Test that it's accessible from your local machine
   - Verify health endpoint responds

3. **Update VS Code Settings:**
   Once servers are running on Synology, update your VS Code settings to point to Synology URLs:
   ```json
   {
     "github.copilot.chat.useMcp": true,
     "github.copilot.mcpServers": [
       "markitdown-mcp"
     ]
   }
   ```

4. **Add More Servers:**
   - Once first server works, add others one by one
   - Test each before adding the next

## 📞 **Need Help?**

If you're still having issues:
1. Check if you can access the basic Synology web interface
2. Verify the Docker package is installed on your Synology
3. Try the alternative ports mentioned above
4. Consider using SSH if web interface isn't accessible

The key is getting that first MCP server running on your Synology NAS, then the rest will follow the same pattern!
