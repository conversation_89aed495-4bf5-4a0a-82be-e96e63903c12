import fetch from 'node-fetch';

async function testToolsList() {
  try {
    console.log('Testing tools.list endpoint...');    const response = await fetch('http://127.0.0.1:3000/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: '1',
        method: 'tools.list',
        params: {}
      })
    });    const text = await response.text();
    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers);
    console.log('Response body:', text);
    try {
      const data = JSON.parse(text);
      console.log('Tools list response:', JSON.stringify(data, null, 2));
    } catch (e) {
      console.error('Failed to parse response as JSON:', e);
    }
  } catch (error) {
    console.error('Error testing tools list:', error);
  }
}

testToolsList();
