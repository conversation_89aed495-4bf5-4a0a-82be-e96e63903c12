FROM node:20-alpine AS builder

WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./
COPY tsconfig.json ./

# Install all dependencies including dev dependencies for building
RUN npm install

# Copy source code
COPY index.ts ./

# Build TypeScript
RUN npm run build

FROM node:20-alpine AS release

WORKDIR /app

# Copy built files and package files
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/package*.json ./

# Install only production dependencies and curl for health checks
RUN npm ci --only=production --ignore-scripts && \
    apk add --no-cache curl bash

# Copy entrypoint script
COPY docker-entrypoint.sh /app/docker-entrypoint.sh
RUN chmod +x /app/docker-entrypoint.sh

# Create a non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Create workspace directories
RUN mkdir -p /app/workspace /app/home && \
    chown -R nodejs:nodejs /app

USER nodejs

EXPOSE 3002

# Use the entrypoint script
ENTRYPOINT ["/app/docker-entrypoint.sh"]
