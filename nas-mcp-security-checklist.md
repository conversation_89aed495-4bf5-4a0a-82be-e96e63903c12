# NAS MCP Server Security Checklist

## Network Security
- [ ] Ensure port 7031 is only accessible from trusted networks
- [ ] Consider using VPN or SSH tunneling for remote access
- [ ] Implement authentication if the MCP server supports it
- [ ] Use HTTPS if possible (configure SSL certificates)

## Container Security
- [ ] Run container with minimal privileges
- [ ] Limit container resource usage
- [ ] Use read-only volumes where possible
- [ ] Implement proper logging and monitoring

## VS Code Configuration
- [ ] Enable MCP server confirmation prompts
- [ ] Review tool permissions before allowing execution
- [ ] Monitor MCP server logs for suspicious activity

## Firewall Configuration
```bash
# Example Synology firewall rules
# Allow only specific IP ranges to access port 7031
# Block all other external access
```

## Authentication (if supported)
- [ ] Configure API keys or tokens
- [ ] Use environment variables for credentials
- [ ] Rotate credentials regularly
