#!/usr/bin/env pwsh
# Synology MCP Manager
# Comprehensive management script for MCP servers running on Synology NAS

param (
    [Parameter(Position=0)]
    [ValidateSet("status", "start", "stop", "restart", "logs", "health", "deploy", "backup", "restore", "help")]
    [string]$Action = "status",
    
    [Parameter()]
    [string]$SynologyHost = "************",
    
    [Parameter()]
    [string]$SynologyPort = "7031",
    
    [Parameter()]
    [string]$ServerName = "",
    
    [Parameter()]
    [switch]$All = $false
)

# MCP Server definitions
$mcpServers = @{
    "markitdown" = @{
        Name = "MarkItDown MCP"
        Container = "markitdown-mcp-server"
        Port = "3001"
        HealthEndpoint = "/health"
        Directory = "markitdown-mcp-server"
    }
    "outlook" = @{
        Name = "Outlook MCP"
        Container = "outlook-mcp-server"
        Port = "3002"
        HealthEndpoint = "/health"
        Directory = "outlook-mcp-server"
    }
    "google-ai" = @{
        Name = "Google AI Edge MCP"
        Container = "google-ai-edge-mcp-server"
        Port = "3005"
        HealthEndpoint = "/health"
        Directory = "google-ai-edge-mcp-server"
    }
    "dji" = @{
        Name = "DJI MCP"
        Container = "dji-mcp-server"
        Port = "3010"
        HealthEndpoint = "/health"
        Directory = "dji-mcp-server"
    }
    "elevenlabs" = @{
        Name = "ElevenLabs TTS"
        Container = "elevenlabs-tts"
        Port = "3015"
        HealthEndpoint = "/health"
        Directory = "elevenlabs-integration"
    }
    "filesystem" = @{
        Name = "Filesystem MCP"
        Container = "fs-mcp-final"
        Port = "3012"
        HealthEndpoint = "/health"
        Directory = "fs-mcp-server"
    }
    "openmanus" = @{
        Name = "OpenManus MCP"
        Container = "openmanus-mcp-server"
        Port = "3013"
        HealthEndpoint = "/health"
        Directory = "openmanus-mcp-server"
    }
    "strands" = @{
        Name = "Strands Agents MCP"
        Container = "strands-agents-mcp-server"
        Port = "3014"
        HealthEndpoint = "/health"
        Directory = "strands-agents-mcp-server"
    }
    "ollama" = @{
        Name = "Ollama Streaming MCP"
        Container = "ollama-streaming-mcp"
        Port = "3006"
        HealthEndpoint = "/health"
        Directory = "ollama-streaming-mcp"
    }
    "weather" = @{
        Name = "Weather Streaming MCP"
        Container = "weather-streaming-mcp"
        Port = "3007"
        HealthEndpoint = "/health"
        Directory = "weather-streaming-mcp"
    }
    "email" = @{
        Name = "Email Streaming MCP"
        Container = "email-streaming-mcp"
        Port = "3008"
        HealthEndpoint = "/health"
        Directory = "email-streaming-mcp"
    }
    "monitoring" = @{
        Name = "System Monitoring MCP"
        Container = "system-monitoring-mcp"
        Port = "3011"
        HealthEndpoint = "/health"
        Directory = "system-monitoring-mcp"
    }
}

# Utility functions
function Write-ColoredStatus {
    param (
        [string]$Message,
        [string]$Status,
        [ConsoleColor]$Color = 'White'
    )
    Write-Host $Message.PadRight(40) -NoNewline
    Write-Host "[$Status]" -ForegroundColor $Color
}

function Get-ContainerStatus {
    param ([string]$ContainerName)
    
    $running = docker ps -q --filter "name=$ContainerName" 2>$null
    if ($running) {
        return "Running"
    }
    
    $exists = docker ps -aq --filter "name=$ContainerName" 2>$null
    if ($exists) {
        return "Stopped"
    }
    
    return "Not Found"
}

function Test-ServerHealth {
    param (
        [string]$Port,
        [string]$HealthEndpoint = "/health"
    )
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$Port$HealthEndpoint" -TimeoutSec 5 -ErrorAction SilentlyContinue
        return $response.StatusCode -eq 200
    } catch {
        return $false
    }
}

function Show-ServerStatus {
    param ([hashtable]$Servers = $mcpServers)
    
    Write-Host "`n🔍 MCP Server Status Report" -ForegroundColor Cyan
    Write-Host "============================" -ForegroundColor Cyan
    Write-Host "Synology NAS: https://$SynologyHost:$SynologyPort" -ForegroundColor Yellow
    Write-Host ""
    
    $runningCount = 0
    $totalCount = $Servers.Count
    
    foreach ($key in $Servers.Keys) {
        $server = $Servers[$key]
        $status = Get-ContainerStatus -ContainerName $server.Container
        $health = if ($status -eq "Running") { Test-ServerHealth -Port $server.Port } else { $false }
        
        $statusColor = switch ($status) {
            "Running" { if ($health) { "Green" } else { "Yellow" } }
            "Stopped" { "Red" }
            "Not Found" { "Magenta" }
        }
        
        $healthIcon = if ($health) { "🟢" } else { "🔴" }
        
        Write-Host "$($server.Name)".PadRight(25) -NoNewline
        Write-Host "$status".PadRight(12) -ForegroundColor $statusColor -NoNewline
        Write-Host "$healthIcon Health" -NoNewline
        Write-Host "  Port: $($server.Port)" -ForegroundColor Gray
        
        if ($status -eq "Running") { $runningCount++ }
    }
    
    Write-Host "`n📊 Summary: $runningCount/$totalCount servers running" -ForegroundColor Cyan
    
    # Show Synology connectivity
    Write-Host "`n🌐 Synology Connectivity:" -ForegroundColor Cyan
    try {
        $synologyResponse = Invoke-WebRequest -Uri "https://$SynologyHost:$SynologyPort" -TimeoutSec 5 -SkipCertificateCheck -ErrorAction SilentlyContinue
        Write-ColoredStatus "Synology NAS" "Accessible" "Green"
    } catch {
        Write-ColoredStatus "Synology NAS" "Unreachable" "Red"
    }
    
    # Test Ollama connectivity
    try {
        $ollamaResponse = Invoke-WebRequest -Uri "http://$SynologyHost:11434/api/tags" -TimeoutSec 5 -ErrorAction SilentlyContinue
        Write-ColoredStatus "Ollama Server" "Accessible" "Green"
    } catch {
        Write-ColoredStatus "Ollama Server" "Unreachable" "Red"
    }
}

function Start-MCPServer {
    param (
        [string]$ServerKey,
        [hashtable]$ServerConfig
    )
    
    Write-Host "🚀 Starting $($ServerConfig.Name)..." -ForegroundColor Yellow
    
    $status = Get-ContainerStatus -ContainerName $ServerConfig.Container
    
    if ($status -eq "Running") {
        Write-ColoredStatus "  Already running" "OK" "Green"
        return $true
    }
    
    try {
        Push-Location $ServerConfig.Directory
        
        if ($status -eq "Stopped") {
            docker start $ServerConfig.Container | Out-Null
        } else {
            docker-compose up --build -d | Out-Null
        }
        
        # Wait for startup
        Start-Sleep -Seconds 5
        
        $newStatus = Get-ContainerStatus -ContainerName $ServerConfig.Container
        if ($newStatus -eq "Running") {
            Write-ColoredStatus "  Started successfully" "OK" "Green"
            return $true
        } else {
            Write-ColoredStatus "  Failed to start" "ERROR" "Red"
            return $false
        }
    } catch {
        Write-ColoredStatus "  Error: $($_.Exception.Message)" "ERROR" "Red"
        return $false
    } finally {
        Pop-Location
    }
}

function Stop-MCPServer {
    param (
        [string]$ServerKey,
        [hashtable]$ServerConfig
    )
    
    Write-Host "🛑 Stopping $($ServerConfig.Name)..." -ForegroundColor Yellow
    
    $status = Get-ContainerStatus -ContainerName $ServerConfig.Container
    
    if ($status -ne "Running") {
        Write-ColoredStatus "  Already stopped" "OK" "Green"
        return $true
    }
    
    try {
        docker stop $ServerConfig.Container | Out-Null
        Write-ColoredStatus "  Stopped successfully" "OK" "Green"
        return $true
    } catch {
        Write-ColoredStatus "  Error: $($_.Exception.Message)" "ERROR" "Red"
        return $false
    }
}

function Show-ServerLogs {
    param (
        [string]$ServerKey,
        [hashtable]$ServerConfig,
        [int]$Lines = 50
    )
    
    Write-Host "📋 Logs for $($ServerConfig.Name):" -ForegroundColor Cyan
    Write-Host "================================" -ForegroundColor Cyan
    
    try {
        docker logs $ServerConfig.Container --tail $Lines
    } catch {
        Write-Host "❌ Could not retrieve logs: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Deploy-ToSynology {
    Write-Host "🚀 Deploying MCP servers to Synology NAS..." -ForegroundColor Cyan
    
    # Create deployment package
    $deploymentDir = "synology-mcp-deployment"
    if (Test-Path $deploymentDir) {
        Remove-Item $deploymentDir -Recurse -Force
    }
    New-Item -ItemType Directory -Path $deploymentDir | Out-Null
    
    # Copy essential files
    foreach ($key in $mcpServers.Keys) {
        $server = $mcpServers[$key]
        $sourceDir = $server.Directory
        $targetDir = Join-Path $deploymentDir $server.Directory
        
        if (Test-Path $sourceDir) {
            Write-Host "📦 Packaging $($server.Name)..." -ForegroundColor Yellow
            Copy-Item $sourceDir $targetDir -Recurse
        }
    }
    
    # Create deployment script
    $deployScript = @"
#!/bin/bash
# Synology MCP Deployment Script
echo "Deploying MCP servers to Synology NAS..."

# Create network
docker network create synology-mcp 2>/dev/null || true

# Deploy each server
"@
    
    foreach ($key in $mcpServers.Keys) {
        $server = $mcpServers[$key]
        $deployScript += @"

echo "Starting $($server.Name)..."
cd $($server.Directory)
docker-compose up --build -d
cd ..
"@
    }
    
    Set-Content -Path (Join-Path $deploymentDir "deploy.sh") -Value $deployScript
    
    Write-Host "✅ Deployment package created in $deploymentDir" -ForegroundColor Green
    Write-Host "📋 Next steps:" -ForegroundColor Cyan
    Write-Host "   1. Copy the $deploymentDir folder to your Synology NAS" -ForegroundColor White
    Write-Host "   2. SSH into your Synology NAS" -ForegroundColor White
    Write-Host "   3. Run: chmod +x deploy.sh && ./deploy.sh" -ForegroundColor White
}

function Show-Help {
    Write-Host "Synology MCP Manager" -ForegroundColor Cyan
    Write-Host "===================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\synology-mcp-manager.ps1 [action] [options]" -ForegroundColor White
    Write-Host ""
    Write-Host "Actions:" -ForegroundColor Yellow
    Write-Host "  status    - Show status of all MCP servers (default)"
    Write-Host "  start     - Start MCP server(s)"
    Write-Host "  stop      - Stop MCP server(s)"
    Write-Host "  restart   - Restart MCP server(s)"
    Write-Host "  logs      - Show logs for a specific server"
    Write-Host "  health    - Check health of all servers"
    Write-Host "  deploy    - Create Synology deployment package"
    Write-Host "  backup    - Backup MCP configurations"
    Write-Host "  restore   - Restore MCP configurations"
    Write-Host "  help      - Show this help"
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -ServerName <name>  - Target specific server (use with start/stop/logs)"
    Write-Host "  -All               - Apply action to all servers"
    Write-Host "  -SynologyHost <ip> - Synology NAS IP (default: ************)"
    Write-Host "  -SynologyPort <port> - Synology port (default: 7031)"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\synology-mcp-manager.ps1 status"
    Write-Host "  .\synology-mcp-manager.ps1 start -ServerName markitdown"
    Write-Host "  .\synology-mcp-manager.ps1 start -All"
    Write-Host "  .\synology-mcp-manager.ps1 logs -ServerName outlook"
    Write-Host "  .\synology-mcp-manager.ps1 deploy"
    Write-Host ""
    Write-Host "Available Servers:" -ForegroundColor Yellow
    foreach ($key in $mcpServers.Keys) {
        Write-Host "  $key - $($mcpServers[$key].Name)" -ForegroundColor Gray
    }
}

# Main execution logic
switch ($Action) {
    "status" {
        Show-ServerStatus
    }
    
    "start" {
        if ($All) {
            foreach ($key in $mcpServers.Keys) {
                Start-MCPServer -ServerKey $key -ServerConfig $mcpServers[$key]
            }
        } elseif ($ServerName -and $mcpServers.ContainsKey($ServerName)) {
            Start-MCPServer -ServerKey $ServerName -ServerConfig $mcpServers[$ServerName]
        } else {
            Write-Host "❌ Please specify -ServerName or use -All" -ForegroundColor Red
        }
        Show-ServerStatus
    }
    
    "stop" {
        if ($All) {
            foreach ($key in $mcpServers.Keys) {
                Stop-MCPServer -ServerKey $key -ServerConfig $mcpServers[$key]
            }
        } elseif ($ServerName -and $mcpServers.ContainsKey($ServerName)) {
            Stop-MCPServer -ServerKey $ServerName -ServerConfig $mcpServers[$ServerName]
        } else {
            Write-Host "❌ Please specify -ServerName or use -All" -ForegroundColor Red
        }
        Show-ServerStatus
    }
    
    "restart" {
        if ($All) {
            foreach ($key in $mcpServers.Keys) {
                Stop-MCPServer -ServerKey $key -ServerConfig $mcpServers[$key]
                Start-Sleep -Seconds 2
                Start-MCPServer -ServerKey $key -ServerConfig $mcpServers[$key]
            }
        } elseif ($ServerName -and $mcpServers.ContainsKey($ServerName)) {
            Stop-MCPServer -ServerKey $ServerName -ServerConfig $mcpServers[$ServerName]
            Start-Sleep -Seconds 2
            Start-MCPServer -ServerKey $ServerName -ServerConfig $mcpServers[$ServerName]
        } else {
            Write-Host "❌ Please specify -ServerName or use -All" -ForegroundColor Red
        }
        Show-ServerStatus
    }
    
    "logs" {
        if ($ServerName -and $mcpServers.ContainsKey($ServerName)) {
            Show-ServerLogs -ServerKey $ServerName -ServerConfig $mcpServers[$ServerName]
        } else {
            Write-Host "❌ Please specify -ServerName" -ForegroundColor Red
        }
    }
    
    "health" {
        Show-ServerStatus
    }
    
    "deploy" {
        Deploy-ToSynology
    }
    
    "backup" {
        Write-Host "🔄 Backup functionality coming soon..." -ForegroundColor Yellow
    }
    
    "restore" {
        Write-Host "🔄 Restore functionality coming soon..." -ForegroundColor Yellow
    }
    
    "help" {
        Show-Help
    }
    
    default {
        Write-Host "❌ Unknown action: $Action" -ForegroundColor Red
        Show-Help
    }
}
