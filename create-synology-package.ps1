#!/usr/bin/env pwsh
# Create Synology MCP Deployment Package

param (
    [Parameter()]
    [string]$SynologyHost = "************",
    [Parameter()]
    [string]$OutputDir = "synology-mcp-deployment"
)

Write-Host "Creating Synology MCP Deployment Package" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

# Clean and create output directory
if (Test-Path $OutputDir) {
    Remove-Item $OutputDir -Recurse -Force
}
New-Item -ItemType Directory -Path $OutputDir | Out-Null

# MCP Servers to deploy
$servers = @(
    @{ Name = "MarkItDown MCP"; Dir = "markitdown-mcp-server"; Port = "3001" },
    @{ Name = "Filesystem MCP"; Dir = "fs-mcp-server"; Port = "3012" },
    @{ Name = "Outlook MCP"; Dir = "outlook-mcp-server"; Port = "3002" },
    @{ Name = "Google AI MCP"; Dir = "google-ai-edge-mcp-server"; Port = "3005" },
    @{ Name = "OpenManus MCP"; Dir = "openmanus-mcp-server"; Port = "3013" }
)

$successCount = 0

foreach ($server in $servers) {
    Write-Host "Packaging $($server.Name)..." -ForegroundColor Yellow
    
    $sourceDir = $server.Dir
    $targetDir = Join-Path $OutputDir $server.Dir
    
    if (Test-Path $sourceDir) {
        # Copy server directory
        Copy-Item $sourceDir $targetDir -Recurse -Force
        
        # Update docker-compose.yml for Synology
        $composePath = Join-Path $targetDir "docker-compose.yml"
        if (Test-Path $composePath) {
            $content = Get-Content $composePath -Raw
            $content = $content -replace 'OLLAMA_BASE_URL=http://192\.168\.1\.26:11434', "OLLAMA_BASE_URL=http://$SynologyHost`:11434"
            $content = $content -replace 'host\.docker\.internal', $SynologyHost
            Set-Content $composePath $content
        }
        
        # Create deployment script
        $deployScript = @"
#!/bin/bash
echo "Deploying $($server.Name)..."
docker network create mcp-network 2>/dev/null || true
docker stop $($server.Dir) 2>/dev/null || true
docker rm $($server.Dir) 2>/dev/null || true
cd $($server.Dir)
docker-compose up --build -d
sleep 5
if docker ps | grep -q $($server.Dir); then
    echo "Success: $($server.Name) is running"
    echo "URL: http://$SynologyHost`:$($server.Port)"
else
    echo "Failed: $($server.Name) deployment failed"
    docker logs $($server.Dir) --tail 10
fi
"@
        Set-Content (Join-Path $targetDir "deploy.sh") $deployScript
        
        Write-Host "  ✅ Packaged $($server.Name)" -ForegroundColor Green
        $successCount++
    }
    else {
        Write-Host "  ❌ Directory not found: $sourceDir" -ForegroundColor Red
    }
}

# Create master deployment script
$masterScript = @"
#!/bin/bash
echo "Deploying MCP Servers to Synology NAS"
echo "====================================="

docker network create mcp-network 2>/dev/null || true

"@

foreach ($server in $servers) {
    $masterScript += @"
echo ""
echo "Deploying $($server.Name)..."
cd $($server.Dir)
chmod +x deploy.sh
./deploy.sh
cd ..

"@
}

$masterScript += @"
echo ""
echo "Deployment Summary:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "network=mcp-network"
echo ""
echo "Deployment completed!"
"@

Set-Content (Join-Path $OutputDir "deploy-all.sh") $masterScript

# Create README with instructions
$readme = @"
# Synology MCP Server Deployment

## Quick Start

### Option 1: SSH Deployment (Recommended)

1. Copy this entire folder to your Synology NAS:
   - Use File Station to upload to /volume1/docker/
   - Or use SCP: scp -r synology-mcp-deployment admin@$SynologyHost`:/volume1/docker/

2. SSH into your Synology:
   ssh admin@$SynologyHost

3. Navigate and deploy:
   cd /volume1/docker/synology-mcp-deployment
   chmod +x deploy-all.sh
   ./deploy-all.sh

### Option 2: Individual Server Deployment

Deploy one server at a time for testing:

1. MarkItDown MCP (Document conversion):
   cd markitdown-mcp-server
   chmod +x deploy.sh && ./deploy.sh
   Test: http://$SynologyHost`:3001/health

2. Filesystem MCP (File operations):
   cd fs-mcp-server
   chmod +x deploy.sh && ./deploy.sh
   Test: http://$SynologyHost`:3012/health

3. Outlook MCP (Email integration):
   cd outlook-mcp-server
   chmod +x deploy.sh && ./deploy.sh
   Test: http://$SynologyHost`:3002/health

4. Google AI MCP (AI processing):
   cd google-ai-edge-mcp-server
   chmod +x deploy.sh && ./deploy.sh
   Test: http://$SynologyHost`:3005/health

5. OpenManus MCP (FoundationAgents):
   cd openmanus-mcp-server
   chmod +x deploy.sh && ./deploy.sh
   Test: http://$SynologyHost`:3013/health

### Option 3: Synology Docker GUI

1. Open https://$SynologyHost`:7031
2. Go to Docker -> Container
3. For each server directory:
   - Click "Create" -> "Via docker-compose"
   - Upload the docker-compose.yml file
   - Start the container

## Verification

Check all containers are running:
docker ps --filter "network=mcp-network"

Test health endpoints:
curl http://$SynologyHost`:3001/health  # MarkItDown
curl http://$SynologyHost`:3012/health  # Filesystem
curl http://$SynologyHost`:3002/health  # Outlook
curl http://$SynologyHost`:3005/health  # Google AI
curl http://$SynologyHost`:3013/health  # OpenManus

## Troubleshooting

View logs:
docker logs [container-name]

Restart a service:
docker restart [container-name]

Remove and rebuild:
docker stop [container-name]
docker rm [container-name]
cd [server-directory]
docker-compose up --build -d

## Next Steps

After deployment:
1. Update your VS Code settings to use Synology URLs
2. Test MCP integration with GitHub Copilot
3. Add more servers as needed
"@

Set-Content (Join-Path $OutputDir "README.md") $readme

# Create Windows helper script
$windowsHelper = @"
@echo off
echo Synology MCP Deployment Helper
echo ==============================
echo.
echo This package contains MCP servers ready for Synology deployment.
echo.
echo Next steps:
echo 1. Copy the 'synology-mcp-deployment' folder to your Synology NAS
echo 2. SSH to your Synology: ssh admin@$SynologyHost
echo 3. Run: cd /volume1/docker/synology-mcp-deployment
echo 4. Run: chmod +x deploy-all.sh && ./deploy-all.sh
echo.
echo Or use the Synology Docker GUI at: https://$SynologyHost`:7031
echo.
pause
"@

Set-Content (Join-Path $OutputDir "DEPLOY-INSTRUCTIONS.bat") $windowsHelper

Write-Host "`n✅ Deployment package created successfully!" -ForegroundColor Green
Write-Host "📁 Location: $OutputDir" -ForegroundColor Cyan
Write-Host "📊 Packaged: $successCount/$($servers.Count) servers" -ForegroundColor Cyan

Write-Host "`n📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Copy the '$OutputDir' folder to your Synology NAS" -ForegroundColor White
Write-Host "2. SSH to Synology: ssh admin@$SynologyHost" -ForegroundColor White
Write-Host "3. Run deployment: cd /volume1/docker/$OutputDir; chmod +x deploy-all.sh; ./deploy-all.sh" -ForegroundColor White
Write-Host "4. Or use Synology Docker GUI at: https://$SynologyHost`:7031" -ForegroundColor White

Write-Host "`n🎯 Individual Testing:" -ForegroundColor Yellow
Write-Host "Start with MarkItDown MCP first, then add others one by one" -ForegroundColor White
Write-Host "Each server has its own deploy.sh script for individual testing" -ForegroundColor White
