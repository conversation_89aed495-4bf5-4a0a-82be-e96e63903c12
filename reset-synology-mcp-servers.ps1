#!/usr/bin/env pwsh
# Reset Synology NAS MCP Servers Script
# This script helps reset up all MCP servers to run on Synology NAS Docker at https://************:7031

param (
    [Parameter()]
    [string]$SynologyHost = "************",
    
    [Parameter()]
    [string]$SynologyPort = "7031",
    
    [Parameter()]
    [switch]$Force = $false
)

Write-Host "🔄 Resetting MCP Servers for Synology NAS" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "Target: https://$SynologyHost:$SynologyPort" -ForegroundColor Yellow
Write-Host ""

# Define all your MCP servers with their configurations
$mcpServers = @(
    @{
        Name = "MarkItDown MCP Server"
        Directory = "markitdown-mcp-server"
        Container = "markitdown-mcp-server"
        Port = "3001"
        Description = "Document conversion with OCR"
        ComposeFile = "docker-compose.yml"
    },
    @{
        Name = "Outlook MCP Server"
        Directory = "outlook-mcp-server"
        Container = "outlook-mcp-server"
        Port = "3002"
        Description = "Microsoft Outlook integration"
        ComposeFile = "docker-compose.yml"
    },
    @{
        Name = "Google AI Edge MCP Server"
        Directory = "google-ai-edge-mcp-server"
        Container = "google-ai-edge-mcp-server"
        Port = "3005"
        Description = "Google AI Edge processing"
        ComposeFile = "docker-compose.yml"
    },
    @{
        Name = "DJI MCP Server"
        Directory = "dji-mcp-server"
        Container = "dji-mcp-server"
        Port = "3010"
        Description = "DJI drone control"
        ComposeFile = "docker-compose.yml"
    },
    @{
        Name = "ElevenLabs TTS Server"
        Directory = "elevenlabs-integration"
        Container = "elevenlabs-tts"
        Port = "3015"
        Description = "Text-to-speech service"
        ComposeFile = "package.json"
    },
    @{
        Name = "Filesystem MCP Server"
        Directory = "fs-mcp-server"
        Container = "fs-mcp-final"
        Port = "3012"
        Description = "File system operations"
        ComposeFile = "docker-compose.yml"
    },
    @{
        Name = "OpenManus MCP Server"
        Directory = "openmanus-mcp-server"
        Container = "openmanus-mcp-server"
        Port = "3013"
        Description = "FoundationAgents/OpenManus integration"
        ComposeFile = "docker-compose.yml"
    },
    @{
        Name = "Strands Agents MCP Server"
        Directory = "strands-agents-mcp-server"
        Container = "strands-agents-mcp-server"
        Port = "3014"
        Description = "AWS Strands Agents SDK"
        ComposeFile = "docker-compose.yml"
    },
    @{
        Name = "Ollama Streaming MCP"
        Directory = "ollama-streaming-mcp"
        Container = "ollama-streaming-mcp"
        Port = "3006"
        Description = "Ollama AI streaming"
        ComposeFile = "docker-compose.yml"
    },
    @{
        Name = "Weather Streaming MCP"
        Directory = "weather-streaming-mcp"
        Container = "weather-streaming-mcp"
        Port = "3007"
        Description = "Weather data streaming"
        ComposeFile = "docker-compose.yml"
    },
    @{
        Name = "Email Streaming MCP"
        Directory = "email-streaming-mcp"
        Container = "email-streaming-mcp"
        Port = "3008"
        Description = "Email streaming service"
        ComposeFile = "docker-compose.yml"
    },
    @{
        Name = "System Monitoring MCP"
        Directory = "system-monitoring-mcp"
        Container = "system-monitoring-mcp"
        Port = "3011"
        Description = "System monitoring service"
        ComposeFile = "docker-compose.yml"
    }
)

# Function to check if Docker is running
function Test-DockerRunning {
    try {
        $null = docker version 2>$null
        return $true
    } catch {
        return $false
    }
}

# Function to stop and remove existing containers
function Stop-ExistingContainers {
    Write-Host "🛑 Stopping existing MCP containers..." -ForegroundColor Yellow
    
    foreach ($server in $mcpServers) {
        $containerName = $server.Container
        
        # Check if container exists
        $exists = docker ps -aq --filter "name=$containerName" 2>$null
        if ($exists) {
            Write-Host "   Stopping $containerName..." -ForegroundColor Gray
            docker stop $containerName 2>$null | Out-Null
            docker rm $containerName 2>$null | Out-Null
        }
    }
    
    Write-Host "✅ Existing containers stopped and removed" -ForegroundColor Green
}

# Function to update docker-compose files for Synology
function Update-DockerComposeForSynology {
    param (
        [string]$Directory,
        [string]$Port,
        [string]$SynologyHost
    )
    
    $composePath = Join-Path $Directory "docker-compose.yml"
    
    if (Test-Path $composePath) {
        Write-Host "   📝 Updating docker-compose.yml for Synology..." -ForegroundColor Gray
        
        # Read the compose file
        $content = Get-Content $composePath -Raw
        
        # Update environment variables for Synology integration
        $updatedContent = $content -replace 'OLLAMA_BASE_URL=http://192\.168\.1\.26:11434', "OLLAMA_BASE_URL=http://$SynologyHost:11434"
        $updatedContent = $updatedContent -replace 'host\.docker\.internal', $SynologyHost
        
        # Add network configuration if not present
        if ($updatedContent -notmatch 'networks:') {
            $updatedContent += "`n`nnetworks:`n  synology-mcp:`n    external: true"
        }
        
        # Write back the updated content
        Set-Content -Path $composePath -Value $updatedContent
        
        Write-Host "   ✅ Updated for Synology integration" -ForegroundColor Green
    }
}

# Function to start MCP servers
function Start-MCPServers {
    Write-Host "🚀 Starting MCP servers for Synology..." -ForegroundColor Green
    
    $successCount = 0
    $totalServers = $mcpServers.Count
    
    foreach ($server in $mcpServers) {
        Write-Host "`n📦 Setting up $($server.Name)..." -ForegroundColor Yellow
        Write-Host "   Description: $($server.Description)" -ForegroundColor Gray
        Write-Host "   Port: $($server.Port)" -ForegroundColor Gray
        
        try {
            $serverDir = $server.Directory
            
            if (-not (Test-Path $serverDir)) {
                Write-Host "   ❌ Directory not found: $serverDir" -ForegroundColor Red
                continue
            }
            
            # Update configuration for Synology
            Update-DockerComposeForSynology -Directory $serverDir -Port $server.Port -SynologyHost $SynologyHost
            
            # Change to server directory
            Push-Location $serverDir
            
            try {
                if ($server.ComposeFile -eq "docker-compose.yml") {
                    Write-Host "   🏗️  Building and starting container..." -ForegroundColor Blue
                    docker-compose up --build -d 2>$null
                    
                    # Wait for container to start
                    Start-Sleep -Seconds 5
                    
                    # Check if container is running
                    $running = docker ps -q --filter "name=$($server.Container)"
                    if ($running) {
                        Write-Host "   ✅ Successfully started" -ForegroundColor Green
                        $successCount++
                    } else {
                        Write-Host "   ❌ Failed to start" -ForegroundColor Red
                        # Show logs for debugging
                        Write-Host "   📋 Container logs:" -ForegroundColor Gray
                        docker logs $($server.Container) --tail 10 2>$null
                    }
                } elseif ($server.ComposeFile -eq "package.json") {
                    # Handle Node.js services like ElevenLabs
                    Write-Host "   📦 Starting Node.js service..." -ForegroundColor Blue
                    # This would need custom handling for each Node.js service
                    Write-Host "   ⚠️  Node.js service requires manual setup" -ForegroundColor Yellow
                }
            } finally {
                Pop-Location
            }
            
        } catch {
            Write-Host "   ❌ Error: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Write-Host "`n=================================" -ForegroundColor Cyan
    Write-Host "🎯 Summary: $successCount/$totalServers servers started successfully" -ForegroundColor Green
    
    return $successCount -eq $totalServers
}

# Function to create Synology network
function Create-SynologyNetwork {
    Write-Host "🌐 Creating Synology MCP network..." -ForegroundColor Blue
    
    # Check if network exists
    $networkExists = docker network ls --filter "name=synology-mcp" --format "{{.Name}}"
    
    if (-not $networkExists) {
        docker network create synology-mcp 2>$null
        Write-Host "✅ Synology MCP network created" -ForegroundColor Green
    } else {
        Write-Host "✅ Synology MCP network already exists" -ForegroundColor Green
    }
}

# Function to test connectivity to Synology
function Test-SynologyConnectivity {
    Write-Host "🔍 Testing connectivity to Synology NAS..." -ForegroundColor Blue
    
    try {
        # Test HTTPS connection
        $response = Invoke-WebRequest -Uri "https://$SynologyHost:$SynologyPort" -TimeoutSec 10 -SkipCertificateCheck -ErrorAction SilentlyContinue
        Write-Host "✅ Synology NAS is accessible at https://$SynologyHost:$SynologyPort" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "❌ Cannot reach Synology NAS at https://$SynologyHost:$SynologyPort" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Gray
        return $false
    }
}

# Function to show final status
function Show-FinalStatus {
    Write-Host "`n📊 Final MCP Server Status:" -ForegroundColor Cyan
    Write-Host "=============================" -ForegroundColor Cyan
    
    # Show running containers
    $runningContainers = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" --filter "name=mcp"
    
    if ($runningContainers) {
        Write-Host $runningContainers
    } else {
        Write-Host "No MCP containers currently running" -ForegroundColor Yellow
    }
    
    Write-Host "`n🔗 Integration URLs:" -ForegroundColor Cyan
    foreach ($server in $mcpServers) {
        if ($server.Port) {
            Write-Host "   $($server.Name): http://localhost:$($server.Port)" -ForegroundColor Gray
        }
    }
    
    Write-Host "`n🎯 Synology Integration:" -ForegroundColor Cyan
    Write-Host "   Synology NAS: https://$SynologyHost:$SynologyPort" -ForegroundColor Gray
    Write-Host "   Ollama Server: http://$SynologyHost:11434" -ForegroundColor Gray
}

# Main execution
Write-Host "Starting MCP server reset process..." -ForegroundColor White

# Check prerequisites
if (-not (Test-DockerRunning)) {
    Write-Host "❌ Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
    exit 1
}

# Test Synology connectivity
if (-not (Test-SynologyConnectivity)) {
    Write-Host "⚠️  Warning: Cannot reach Synology NAS. Continuing anyway..." -ForegroundColor Yellow
}

# Stop existing containers if Force is specified
if ($Force) {
    Stop-ExistingContainers
}

# Create network
Create-SynologyNetwork

# Start MCP servers
$allStarted = Start-MCPServers

# Show final status
Show-FinalStatus

if ($allStarted) {
    Write-Host "`n🎉 All MCP servers successfully reset for Synology NAS!" -ForegroundColor Green
    Write-Host "You can now access your MCP servers through the Synology Docker interface." -ForegroundColor White
} else {
    Write-Host "`n⚠️  Some MCP servers may need manual attention." -ForegroundColor Yellow
    Write-Host "Check the logs above for specific issues." -ForegroundColor White
}

Write-Host "`n✅ Reset process completed!" -ForegroundColor Green
