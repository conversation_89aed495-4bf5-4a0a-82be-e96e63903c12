#!/usr/bin/env pwsh
# Simple Synology MCP Deployment Package Creator

param (
    [string]$SynologyHost = "************",
    [string]$OutputDir = "synology-mcp-deployment"
)

Write-Host "Creating Synology MCP Deployment Package" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

# Clean and create output directory
if (Test-Path $OutputDir) {
    Remove-Item $OutputDir -Recurse -Force
}
New-Item -ItemType Directory -Path $OutputDir | Out-Null

# MCP Servers to deploy
$servers = @(
    @{ Name = "MarkItDown MCP"; Dir = "markitdown-mcp-server"; Port = "3001" },
    @{ Name = "Filesystem MCP"; Dir = "fs-mcp-server"; Port = "3012" },
    @{ Name = "Outlook MCP"; Dir = "outlook-mcp-server"; Port = "3002" }
)

$successCount = 0

foreach ($server in $servers) {
    Write-Host "Packaging $($server.Name)..." -ForegroundColor Yellow
    
    $sourceDir = $server.Dir
    $targetDir = Join-Path $OutputDir $server.Dir
    
    if (Test-Path $sourceDir) {
        # Copy server directory
        Copy-Item $sourceDir $targetDir -Recurse -Force
        
        # Update docker-compose.yml for Synology
        $composePath = Join-Path $targetDir "docker-compose.yml"
        if (Test-Path $composePath) {
            $content = Get-Content $composePath -Raw
            $content = $content -replace 'host\.docker\.internal', $SynologyHost
            Set-Content $composePath $content
        }
        
        # Create deployment script
        $deployScript = "#!/bin/bash`n"
        $deployScript += "echo 'Deploying $($server.Name)...'`n"
        $deployScript += "docker network create mcp-network 2>/dev/null || true`n"
        $deployScript += "docker stop $($server.Dir) 2>/dev/null || true`n"
        $deployScript += "docker rm $($server.Dir) 2>/dev/null || true`n"
        $deployScript += "cd $($server.Dir)`n"
        $deployScript += "docker-compose up --build -d`n"
        $deployScript += "sleep 5`n"
        $deployScript += "echo 'Deployment completed for $($server.Name)'`n"
        
        Set-Content (Join-Path $targetDir "deploy.sh") $deployScript
        
        Write-Host "  Success: Packaged $($server.Name)" -ForegroundColor Green
        $successCount++
    } else {
        Write-Host "  Error: Directory not found: $sourceDir" -ForegroundColor Red
    }
}

# Create master deployment script
$masterScript = "#!/bin/bash`n"
$masterScript += "echo 'Deploying MCP Servers to Synology NAS'`n"
$masterScript += "echo '====================================='`n"
$masterScript += "docker network create mcp-network 2>/dev/null || true`n"

foreach ($server in $servers) {
    $masterScript += "echo 'Deploying $($server.Name)...'`n"
    $masterScript += "cd $($server.Dir)`n"
    $masterScript += "chmod +x deploy.sh`n"
    $masterScript += "./deploy.sh`n"
    $masterScript += "cd ..`n"
}

$masterScript += "echo 'All deployments completed!'`n"
$masterScript += "docker ps --filter 'network=mcp-network'`n"

Set-Content (Join-Path $OutputDir "deploy-all.sh") $masterScript

# Create README
$readme = @"
# Synology MCP Server Deployment

## Quick Start

1. Copy this folder to your Synology NAS
2. SSH to your Synology: ssh admin@$SynologyHost
3. Navigate: cd /volume1/docker/synology-mcp-deployment
4. Deploy: chmod +x deploy-all.sh && ./deploy-all.sh

## Individual Server Deployment

Test one server at a time:

1. MarkItDown MCP:
   cd markitdown-mcp-server
   chmod +x deploy.sh && ./deploy.sh

2. Filesystem MCP:
   cd fs-mcp-server
   chmod +x deploy.sh && ./deploy.sh

3. Outlook MCP:
   cd outlook-mcp-server
   chmod +x deploy.sh && ./deploy.sh

## Verification

Check containers: docker ps --filter "network=mcp-network"
Test health: curl http://$SynologyHost:3001/health

## Synology Docker GUI Alternative

1. Open https://$SynologyHost:7031
2. Go to Docker -> Container
3. Create containers using the docker-compose.yml files
"@

Set-Content (Join-Path $OutputDir "README.md") $readme

Write-Host ""
Write-Host "Deployment package created successfully!" -ForegroundColor Green
Write-Host "Location: $OutputDir" -ForegroundColor Cyan
Write-Host "Packaged: $successCount servers" -ForegroundColor Cyan
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Yellow
Write-Host "1. Copy the folder to your Synology NAS" -ForegroundColor White
Write-Host "2. SSH and run the deployment scripts" -ForegroundColor White
Write-Host "3. Or use Synology Docker GUI" -ForegroundColor White
