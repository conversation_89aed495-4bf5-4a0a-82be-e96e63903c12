{"name": "fs-mcp-server", "version": "0.2.0", "type": "module", "scripts": {"test": "vitest", "start": "node index.js", "build": "tsc"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "cors": "^2.8.5", "diff": "^5.1.0", "eventsource": "^4.0.0", "express": "^4.18.2", "minimatch": "^9.0.3", "ws": "^8.18.2", "zod": "^3.22.4", "zod-to-json-schema": "^3.22.3"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/diff": "^5.0.9", "@types/express": "^4.17.21", "@types/node": "^20.10.0", "tsx": "^4.19.4", "typescript": "^5.3.2", "vitest": "^3.1.4"}}