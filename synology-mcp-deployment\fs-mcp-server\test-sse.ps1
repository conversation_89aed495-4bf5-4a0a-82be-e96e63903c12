$url = "http://localhost:3003/sse"
Write-Host "Connecting to SSE endpoint at $url..."

Add-Type -AssemblyName System.Net.Http

$client = New-Object System.Net.Http.HttpClient
$client.Timeout = [System.Threading.Timeout]::InfiniteTimeSpan

try {
    $response = $client.GetAsync($url, [System.Net.Http.HttpCompletionOption]::ResponseHeadersRead).Result
    $stream = $response.Content.ReadAsStreamAsync().Result
    $reader = New-Object System.IO.StreamReader($stream)

    Write-Host "Connected successfully. Waiting for events..."
    
    while (-not $reader.EndOfStream) {
        $line = $reader.ReadLine()
        if ($line) {
            Write-Host $line
        }
    }
}
catch {
    Write-Host "Error: $_"
}
finally {
    if ($reader) { $reader.Dispose() }
    if ($client) { $client.Dispose() }
}
