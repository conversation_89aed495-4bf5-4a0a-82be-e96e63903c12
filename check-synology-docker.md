# Synology Docker Container Diagnostic

## Quick SSH Commands to Check Your MCP Server

Since the port test failed, let's check what's happening on your Synology. Run these commands via SSH:

### 1. SSH to your Synology:
```bash
ssh swilliams@************
```

### 2. Check if Docker is running:
```bash
docker --version
docker info
```

### 3. Check all containers:
```bash
# List all containers (running and stopped)
docker ps -a

# Look specifically for markitdown containers
docker ps -a | grep markitdown
```

### 4. Check container logs:
```bash
# If container exists, check its logs
docker logs markitdown-mcp-server

# Or if the container name is different:
docker logs $(docker ps -aq --filter "name=markitdown")
```

### 5. Check port bindings:
```bash
# Check what ports are bound
docker port markitdown-mcp-server

# Check if anything is listening on port 3001
netstat -tlnp | grep 3001
```

### 6. Check the deployment directory:
```bash
# Navigate to where you deployed
cd /volume1/docker/mcp-servers/markitdown
ls -la

# Check docker-compose.yml content
cat docker-compose.yml
```

### 7. If container is not running, try to start it:
```bash
# If using docker-compose
cd /volume1/docker/mcp-servers/markitdown
docker-compose up -d

# Or start directly
docker start markitdown-mcp-server

# Or rebuild if needed
docker-compose down
docker-compose up --build -d
```

### 8. Test locally on Synology:
```bash
# Test from within Synology
curl http://localhost:3001/health
curl http://127.0.0.1:3001/health

# Check if the service is responding locally
wget -qO- http://localhost:3001/health
```

## Common Issues and Solutions:

### Issue 1: Container not running
**Solution:** Start the container
```bash
docker-compose up -d
```

### Issue 2: Port not bound correctly
**Check:** Look at docker-compose.yml ports section
```yaml
ports:
  - "3001:3001"  # Should map host:container
```

### Issue 3: Container keeps restarting
**Check:** Container logs for errors
```bash
docker logs markitdown-mcp-server --tail 50
```

### Issue 4: Firewall blocking port
**Check:** Synology firewall settings
- Go to Control Panel > Security > Firewall
- Ensure port 3001 is allowed

## Quick Test Commands:

After making any changes, test with:

```bash
# On Synology (via SSH)
curl http://localhost:3001/health

# From your local machine
curl http://************:3001/health
```

## Expected Results:

If working correctly, you should see:
```json
{"status": "ok", "service": "markitdown-mcp"}
```

## Next Steps:

1. **Run the SSH diagnostic commands above**
2. **Share the output** of `docker ps -a` and `docker logs markitdown-mcp-server`
3. **Check if the container is running** and what errors (if any) are in the logs
4. **Verify the port mapping** in docker-compose.yml
5. **Test locally on Synology first** before testing from your local machine

Once we identify the issue, we can fix it and then proceed to deploy additional MCP servers one by one.
