#!/usr/bin/env pwsh
# Simple Synology MCP Diagnostic Script

param (
    [Parameter()]
    [string]$SynologyHost = "************",
    
    [Parameter()]
    [string]$SynologyPort = "7031"
)

Write-Host "Synology MCP Diagnostic Report" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan
Write-Host "Target Synology: https://$SynologyHost`:$SynologyPort" -ForegroundColor Yellow
Write-Host ""

# Function to test connectivity
function Test-Connection {
    param (
        [string]$TargetHost,
        [string]$Port,
        [string]$Protocol = "http"
    )
    
    try {
        $uri = "$Protocol`://$TargetHost`:$Port"
        if ($Protocol -eq "https") {
            $response = Invoke-WebRequest -Uri $uri -TimeoutSec 5 -SkipCertificateCheck -ErrorAction SilentlyContinue
        } else {
            $response = Invoke-WebRequest -Uri $uri -TimeoutSec 5 -ErrorAction SilentlyContinue
        }
        return $true
    } catch {
        return $false
    }
}

# Check Docker
Write-Host "Docker Environment Check" -ForegroundColor Blue
Write-Host "------------------------" -ForegroundColor Blue

try {
    $dockerVersion = docker version --format "{{.Server.Version}}" 2>$null
    if ($dockerVersion) {
        Write-Host "Docker Server: $dockerVersion" -ForegroundColor Green
    } else {
        Write-Host "Docker Server: Not accessible" -ForegroundColor Red
    }
} catch {
    Write-Host "Docker: Not running" -ForegroundColor Red
}

# Check existing containers
Write-Host "`nExisting MCP Containers" -ForegroundColor Blue
Write-Host "-----------------------" -ForegroundColor Blue

$mcpContainers = docker ps -a --filter "name=mcp" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>$null

if ($mcpContainers) {
    Write-Host $mcpContainers
} else {
    Write-Host "No MCP containers found" -ForegroundColor Yellow
}

# Check network connectivity
Write-Host "`nNetwork Connectivity Check" -ForegroundColor Blue
Write-Host "--------------------------" -ForegroundColor Blue

# Test Synology NAS
Write-Host "Testing Synology NAS..." -NoNewline
$synologyOk = Test-Connection -TargetHost $SynologyHost -Port $SynologyPort -Protocol "https"
if ($synologyOk) {
    Write-Host " OK" -ForegroundColor Green
} else {
    Write-Host " FAILED" -ForegroundColor Red
}

# Test Ollama
Write-Host "Testing Ollama Server..." -NoNewline
$ollamaOk = Test-Connection -TargetHost $SynologyHost -Port "11434" -Protocol "http"
if ($ollamaOk) {
    Write-Host " OK" -ForegroundColor Green
} else {
    Write-Host " FAILED" -ForegroundColor Red
}

# Test local MCP ports
$mcpPorts = @("3001", "3002", "3005", "3006", "3007", "3008", "3009", "3010", "3011", "3012", "3013", "3014", "3015")
$activePorts = @()

foreach ($port in $mcpPorts) {
    $portOk = Test-Connection -TargetHost "localhost" -Port $port -Protocol "http"
    if ($portOk) {
        $activePorts += $port
    }
}

if ($activePorts.Count -gt 0) {
    Write-Host "Active MCP ports: $($activePorts -join ', ')" -ForegroundColor Green
} else {
    Write-Host "No active MCP ports found" -ForegroundColor Yellow
}

# Check MCP directories
Write-Host "`nMCP Server Directories" -ForegroundColor Blue
Write-Host "----------------------" -ForegroundColor Blue

$mcpDirs = @(
    "markitdown-mcp-server",
    "outlook-mcp-server", 
    "google-ai-edge-mcp-server",
    "dji-mcp-server",
    "elevenlabs-integration",
    "fs-mcp-server",
    "openmanus-mcp-server",
    "strands-agents-mcp-server",
    "ollama-streaming-mcp",
    "weather-streaming-mcp",
    "email-streaming-mcp",
    "system-monitoring-mcp"
)

$foundDirs = @()
$missingDirs = @()

foreach ($dir in $mcpDirs) {
    if (Test-Path $dir) {
        $foundDirs += $dir
        $composeFile = Join-Path $dir "docker-compose.yml"
        $hasCompose = Test-Path $composeFile
        $composeStatus = if ($hasCompose) { "OK" } else { "MISSING" }
        $color = if ($hasCompose) { "Green" } else { "Yellow" }
        Write-Host "$($dir.PadRight(30)) docker-compose.yml: $composeStatus" -ForegroundColor $color
    } else {
        $missingDirs += $dir
    }
}

Write-Host "`nDirectory Summary:" -ForegroundColor Cyan
Write-Host "Found: $($foundDirs.Count)" -ForegroundColor Green
Write-Host "Missing: $($missingDirs.Count)" -ForegroundColor $(if ($missingDirs.Count -gt 0) { "Red" } else { "Green" })

if ($missingDirs.Count -gt 0) {
    Write-Host "`nMissing directories:" -ForegroundColor Red
    foreach ($dir in $missingDirs) {
        Write-Host "  $dir" -ForegroundColor Gray
    }
}

# Check VS Code configuration
Write-Host "`nVS Code MCP Configuration" -ForegroundColor Blue
Write-Host "-------------------------" -ForegroundColor Blue

$settingsPath = ".vscode\settings.json"
if (Test-Path $settingsPath) {
    try {
        $settings = Get-Content -Raw $settingsPath | ConvertFrom-Json
        
        $mcpEnabled = $settings.'github.copilot.chat.useMcp'
        $mcpServers = $settings.'github.copilot.mcpServers'
        
        Write-Host "MCP Integration: $(if ($mcpEnabled) { 'Enabled' } else { 'Disabled' })" -ForegroundColor $(if ($mcpEnabled) { "Green" } else { "Red" })
        
        if ($mcpServers) {
            Write-Host "Configured servers: $($mcpServers.Count)" -ForegroundColor Green
            foreach ($server in $mcpServers) {
                Write-Host "  - $server" -ForegroundColor Gray
            }
        } else {
            Write-Host "No MCP servers configured" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "Error reading VS Code settings" -ForegroundColor Red
    }
} else {
    Write-Host "VS Code settings file not found" -ForegroundColor Red
}

# Recommendations
Write-Host "`nRecommendations" -ForegroundColor Blue
Write-Host "---------------" -ForegroundColor Blue

$recommendations = @()

if (-not $synologyOk) {
    $recommendations += "Verify Synology NAS is accessible at https://$SynologyHost`:$SynologyPort"
}

if (-not $ollamaOk) {
    $recommendations += "Ensure Ollama is running on Synology NAS at port 11434"
}

if ($activePorts.Count -eq 0) {
    $recommendations += "No MCP containers running - use reset script to start them"
}

if ($missingDirs.Count -gt 0) {
    $recommendations += "Some MCP server directories are missing"
}

if ($recommendations.Count -eq 0) {
    Write-Host "No issues found! Your setup looks good." -ForegroundColor Green
} else {
    foreach ($rec in $recommendations) {
        Write-Host "- $rec" -ForegroundColor Yellow
    }
}

# Next steps
Write-Host "`nNext Steps" -ForegroundColor Blue
Write-Host "----------" -ForegroundColor Blue

Write-Host "1. Run the reset script:" -ForegroundColor White
Write-Host "   .\reset-synology-mcp-servers.ps1 -Force" -ForegroundColor Gray

Write-Host "`n2. Manage your servers:" -ForegroundColor White
Write-Host "   .\synology-mcp-manager.ps1 status" -ForegroundColor Gray

Write-Host "`n3. Install servers individually:" -ForegroundColor White
Write-Host "   .\install-mcp-individual.ps1 -ServerName markitdown" -ForegroundColor Gray

Write-Host "`nDiagnostic completed!" -ForegroundColor Green
