#!/usr/bin/env pwsh
# Individual MCP Server Installer and Tester
# Install and test MCP servers one at a time for Synology integration

param (
    [Parameter(Mandatory=$true)]
    [ValidateSet("markitdown", "outlook", "google-ai", "dji", "elevenlabs", "filesystem", "openmanus", "strands", "ollama", "weather", "email", "monitoring", "list")]
    [string]$ServerName,
    
    [Parameter()]
    [string]$SynologyHost = "************",
    
    [Parameter()]
    [string]$SynologyPort = "7031",
    
    [Parameter()]
    [switch]$Test = $false,
    
    [Parameter()]
    [switch]$Uninstall = $false,
    
    [Parameter()]
    [switch]$Logs = $false,
    
    [Parameter()]
    [switch]$Force = $false
)

# MCP Server definitions
$mcpServers = @{
    "markitdown" = @{
        Name = "MarkItDown MCP Server"
        Container = "markitdown-mcp-server"
        Port = "3001"
        Directory = "markitdown-mcp-server"
        HealthEndpoint = "/health"
        TestCommand = "curl -s http://localhost:3001/health"
        Description = "Document conversion with OCR support"
    }
    "outlook" = @{
        Name = "Outlook MCP Server"
        Container = "outlook-mcp-server"
        Port = "3002"
        Directory = "outlook-mcp-server"
        HealthEndpoint = "/health"
        TestCommand = "curl -s http://localhost:3002/health"
        Description = "Microsoft Outlook email integration"
    }
    "google-ai" = @{
        Name = "Google AI Edge MCP Server"
        Container = "google-ai-edge-mcp-server"
        Port = "3005"
        Directory = "google-ai-edge-mcp-server"
        HealthEndpoint = "/health"
        TestCommand = "curl -s http://localhost:3005/health"
        Description = "Google AI Edge processing"
    }
    "dji" = @{
        Name = "DJI MCP Server"
        Container = "dji-mcp-server"
        Port = "3010"
        Directory = "dji-mcp-server"
        HealthEndpoint = "/health"
        TestCommand = "curl -s http://localhost:3010/health"
        Description = "DJI drone control and telemetry"
    }
    "elevenlabs" = @{
        Name = "ElevenLabs TTS Server"
        Container = "elevenlabs-tts"
        Port = "3015"
        Directory = "elevenlabs-integration"
        HealthEndpoint = "/health"
        TestCommand = "curl -s http://localhost:3015/health"
        Description = "Text-to-speech service"
    }
    "filesystem" = @{
        Name = "Filesystem MCP Server"
        Container = "fs-mcp-final"
        Port = "3012"
        Directory = "fs-mcp-server"
        HealthEndpoint = "/health"
        TestCommand = "curl -s http://localhost:3012/health"
        Description = "File system operations"
    }
    "openmanus" = @{
        Name = "OpenManus MCP Server"
        Container = "openmanus-mcp-server"
        Port = "3013"
        Directory = "openmanus-mcp-server"
        HealthEndpoint = "/health"
        TestCommand = "curl -s http://localhost:3013/health"
        Description = "FoundationAgents/OpenManus integration"
    }
    "strands" = @{
        Name = "Strands Agents MCP Server"
        Container = "strands-agents-mcp-server"
        Port = "3014"
        Directory = "strands-agents-mcp-server"
        HealthEndpoint = "/health"
        TestCommand = "curl -s http://localhost:3014/health"
        Description = "AWS Strands Agents SDK"
    }
    "ollama" = @{
        Name = "Ollama Streaming MCP"
        Container = "ollama-streaming-mcp"
        Port = "3006"
        Directory = "ollama-streaming-mcp"
        HealthEndpoint = "/health"
        TestCommand = "curl -s http://localhost:3006/health"
        Description = "Ollama AI streaming service"
    }
    "weather" = @{
        Name = "Weather Streaming MCP"
        Container = "weather-streaming-mcp"
        Port = "3007"
        Directory = "weather-streaming-mcp"
        HealthEndpoint = "/health"
        TestCommand = "curl -s http://localhost:3007/health"
        Description = "Weather data streaming"
    }
    "email" = @{
        Name = "Email Streaming MCP"
        Container = "email-streaming-mcp"
        Port = "3008"
        Directory = "email-streaming-mcp"
        HealthEndpoint = "/health"
        TestCommand = "curl -s http://localhost:3008/health"
        Description = "Email streaming service"
    }
    "monitoring" = @{
        Name = "System Monitoring MCP"
        Container = "system-monitoring-mcp"
        Port = "3011"
        Directory = "system-monitoring-mcp"
        HealthEndpoint = "/health"
        TestCommand = "curl -s http://localhost:3011/health"
        Description = "System monitoring and metrics"
    }
}

# Utility functions
function Write-Status {
    param (
        [string]$Message,
        [string]$Status,
        [ConsoleColor]$Color = 'White'
    )
    Write-Host $Message.PadRight(50) -NoNewline
    Write-Host "[$Status]" -ForegroundColor $Color
}

function Get-ContainerStatus {
    param ([string]$ContainerName)
    
    $running = docker ps -q --filter "name=$ContainerName" 2>$null
    if ($running) {
        return "Running"
    }
    
    $exists = docker ps -aq --filter "name=$ContainerName" 2>$null
    if ($exists) {
        return "Stopped"
    }
    
    return "Not Found"
}

function Test-ServerHealth {
    param (
        [string]$Port,
        [string]$HealthEndpoint = "/health"
    )
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$Port$HealthEndpoint" -TimeoutSec 5 -ErrorAction SilentlyContinue
        return $response.StatusCode -eq 200
    } catch {
        return $false
    }
}

function Show-AvailableServers {
    Write-Host "Available MCP Servers:" -ForegroundColor Cyan
    Write-Host "=====================" -ForegroundColor Cyan
    
    foreach ($key in $mcpServers.Keys | Sort-Object) {
        $server = $mcpServers[$key]
        $status = Get-ContainerStatus -ContainerName $server.Container
        $statusColor = switch ($status) {
            "Running" { "Green" }
            "Stopped" { "Yellow" }
            "Not Found" { "Red" }
        }
        
        Write-Host "$key".PadRight(15) -NoNewline
        Write-Host "$($server.Name)".PadRight(30) -NoNewline
        Write-Host "[$status]".PadRight(12) -ForegroundColor $statusColor -NoNewline
        Write-Host "Port: $($server.Port)" -ForegroundColor Gray
        Write-Host "    $($server.Description)" -ForegroundColor Gray
        Write-Host ""
    }
}

function Install-MCPServer {
    param (
        [string]$ServerKey,
        [hashtable]$ServerConfig
    )
    
    Write-Host "Installing $($ServerConfig.Name)" -ForegroundColor Cyan
    Write-Host "================================" -ForegroundColor Cyan
    Write-Host "Description: $($ServerConfig.Description)" -ForegroundColor Gray
    Write-Host "Port: $($ServerConfig.Port)" -ForegroundColor Gray
    Write-Host ""
    
    # Check if directory exists
    if (-not (Test-Path $ServerConfig.Directory)) {
        Write-Status "Directory check" "MISSING" "Red"
        Write-Host "Error: Directory $($ServerConfig.Directory) not found" -ForegroundColor Red
        return $false
    }
    Write-Status "Directory check" "OK" "Green"
    
    # Check if docker-compose.yml exists
    $composePath = Join-Path $ServerConfig.Directory "docker-compose.yml"
    if (-not (Test-Path $composePath)) {
        Write-Status "Docker Compose file" "MISSING" "Red"
        Write-Host "Error: docker-compose.yml not found in $($ServerConfig.Directory)" -ForegroundColor Red
        return $false
    }
    Write-Status "Docker Compose file" "OK" "Green"
    
    # Stop existing container if Force is specified
    $currentStatus = Get-ContainerStatus -ContainerName $ServerConfig.Container
    if ($currentStatus -ne "Not Found" -and $Force) {
        Write-Host "Stopping existing container..." -ForegroundColor Yellow
        docker stop $ServerConfig.Container 2>$null | Out-Null
        docker rm $ServerConfig.Container 2>$null | Out-Null
        Write-Status "Container cleanup" "OK" "Green"
    }
    
    # Update configuration for Synology
    Write-Host "Updating configuration for Synology..." -ForegroundColor Blue
    try {
        Push-Location $ServerConfig.Directory
        
        # Read and update docker-compose.yml
        $composeContent = Get-Content "docker-compose.yml" -Raw
        
        # Update Ollama URL for Synology
        $updatedContent = $composeContent -replace 'OLLAMA_BASE_URL=http://192\.168\.1\.26:11434', "OLLAMA_BASE_URL=http://$SynologyHost`:11434"
        $updatedContent = $updatedContent -replace 'host\.docker\.internal', $SynologyHost
        
        # Add Synology network if not present
        if ($updatedContent -notmatch 'networks:') {
            $updatedContent += "`n`nnetworks:`n  synology-mcp:`n    external: true"
        }
        
        Set-Content "docker-compose.yml" $updatedContent
        Write-Status "Synology configuration" "UPDATED" "Green"
        
        # Build and start container
        Write-Host "Building and starting container..." -ForegroundColor Blue
        $buildOutput = docker-compose up --build -d 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Status "Container build" "SUCCESS" "Green"
        } else {
            Write-Status "Container build" "FAILED" "Red"
            Write-Host "Build output:" -ForegroundColor Red
            Write-Host $buildOutput -ForegroundColor Gray
            return $false
        }
        
    } finally {
        Pop-Location
    }
    
    # Wait for container to start
    Write-Host "Waiting for container to start..." -ForegroundColor Blue
    Start-Sleep -Seconds 10
    
    # Verify container is running
    $newStatus = Get-ContainerStatus -ContainerName $ServerConfig.Container
    if ($newStatus -eq "Running") {
        Write-Status "Container status" "RUNNING" "Green"
    } else {
        Write-Status "Container status" "FAILED" "Red"
        return $false
    }
    
    # Test health endpoint
    Write-Host "Testing health endpoint..." -ForegroundColor Blue
    Start-Sleep -Seconds 5
    
    $healthOk = Test-ServerHealth -Port $ServerConfig.Port
    if ($healthOk) {
        Write-Status "Health check" "PASSED" "Green"
    } else {
        Write-Status "Health check" "FAILED" "Yellow"
        Write-Host "Note: Health endpoint may not be available yet" -ForegroundColor Yellow
    }
    
    Write-Host "`nInstallation Summary:" -ForegroundColor Cyan
    Write-Host "Server: $($ServerConfig.Name)" -ForegroundColor White
    Write-Host "Container: $($ServerConfig.Container)" -ForegroundColor White
    Write-Host "Port: $($ServerConfig.Port)" -ForegroundColor White
    Write-Host "Status: Running" -ForegroundColor Green
    Write-Host "Health: $(if ($healthOk) { 'OK' } else { 'Pending' })" -ForegroundColor $(if ($healthOk) { "Green" } else { "Yellow" })
    
    return $true
}

function Test-MCPServer {
    param (
        [string]$ServerKey,
        [hashtable]$ServerConfig
    )
    
    Write-Host "Testing $($ServerConfig.Name)" -ForegroundColor Cyan
    Write-Host "============================" -ForegroundColor Cyan
    
    # Check container status
    $status = Get-ContainerStatus -ContainerName $ServerConfig.Container
    Write-Status "Container Status" $status $(if ($status -eq "Running") { "Green" } else { "Red" })
    
    if ($status -ne "Running") {
        Write-Host "Container is not running. Cannot perform tests." -ForegroundColor Red
        return $false
    }
    
    # Test health endpoint
    $healthOk = Test-ServerHealth -Port $ServerConfig.Port
    Write-Status "Health Endpoint" $(if ($healthOk) { "OK" } else { "FAILED" }) $(if ($healthOk) { "Green" } else { "Red" })
    
    # Test port accessibility
    try {
        $portTest = Test-NetConnection -ComputerName "localhost" -Port $ServerConfig.Port -WarningAction SilentlyContinue
        Write-Status "Port Accessibility" $(if ($portTest.TcpTestSucceeded) { "OK" } else { "FAILED" }) $(if ($portTest.TcpTestSucceeded) { "Green" } else { "Red" })
    } catch {
        Write-Status "Port Accessibility" "UNKNOWN" "Yellow"
    }
    
    # Show container logs (last 10 lines)
    Write-Host "`nRecent Container Logs:" -ForegroundColor Blue
    Write-Host "---------------------" -ForegroundColor Blue
    try {
        $logs = docker logs $ServerConfig.Container --tail 10 2>&1
        Write-Host $logs -ForegroundColor Gray
    } catch {
        Write-Host "Could not retrieve logs" -ForegroundColor Red
    }
    
    return $healthOk
}

function Uninstall-MCPServer {
    param (
        [string]$ServerKey,
        [hashtable]$ServerConfig
    )
    
    Write-Host "Uninstalling $($ServerConfig.Name)" -ForegroundColor Yellow
    Write-Host "==================================" -ForegroundColor Yellow
    
    # Stop and remove container
    $status = Get-ContainerStatus -ContainerName $ServerConfig.Container
    if ($status -ne "Not Found") {
        Write-Host "Stopping container..." -ForegroundColor Blue
        docker stop $ServerConfig.Container 2>$null | Out-Null
        docker rm $ServerConfig.Container 2>$null | Out-Null
        Write-Status "Container removal" "OK" "Green"
    } else {
        Write-Status "Container removal" "NOT NEEDED" "Green"
    }
    
    # Remove images (optional)
    Write-Host "Removing Docker images..." -ForegroundColor Blue
    try {
        Push-Location $ServerConfig.Directory
        docker-compose down --rmi all 2>$null | Out-Null
        Write-Status "Image cleanup" "OK" "Green"
    } catch {
        Write-Status "Image cleanup" "PARTIAL" "Yellow"
    } finally {
        Pop-Location
    }
    
    Write-Host "Uninstallation completed" -ForegroundColor Green
}

function Show-ServerLogs {
    param (
        [string]$ServerKey,
        [hashtable]$ServerConfig
    )
    
    Write-Host "Logs for $($ServerConfig.Name)" -ForegroundColor Cyan
    Write-Host "==============================" -ForegroundColor Cyan
    
    $status = Get-ContainerStatus -ContainerName $ServerConfig.Container
    if ($status -ne "Running") {
        Write-Host "Container is not running" -ForegroundColor Red
        return
    }
    
    try {
        docker logs $ServerConfig.Container --follow
    } catch {
        Write-Host "Could not retrieve logs: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Main execution logic
if ($ServerName -eq "list") {
    Show-AvailableServers
    exit 0
}

if (-not $mcpServers.ContainsKey($ServerName)) {
    Write-Host "Error: Unknown server '$ServerName'" -ForegroundColor Red
    Write-Host "Use -ServerName list to see available servers" -ForegroundColor Yellow
    exit 1
}

$server = $mcpServers[$ServerName]

# Execute requested action
if ($Logs) {
    Show-ServerLogs -ServerKey $ServerName -ServerConfig $server
} elseif ($Test) {
    $testResult = Test-MCPServer -ServerKey $ServerName -ServerConfig $server
    exit $(if ($testResult) { 0 } else { 1 })
} elseif ($Uninstall) {
    Uninstall-MCPServer -ServerKey $ServerName -ServerConfig $server
} else {
    $installResult = Install-MCPServer -ServerKey $ServerName -ServerConfig $server
    if ($installResult) {
        Write-Host "`nInstallation completed successfully!" -ForegroundColor Green
        Write-Host "You can now test the server with:" -ForegroundColor White
        Write-Host "  .\install-mcp-individual.ps1 -ServerName $ServerName -Test" -ForegroundColor Gray
    } else {
        Write-Host "`nInstallation failed!" -ForegroundColor Red
        exit 1
    }
}
