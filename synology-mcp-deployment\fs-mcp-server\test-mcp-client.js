#!/usr/bin/env node

// Simple test client for the MCP filesystem server
import http from 'http';

async function makeRequest(method, params = {}) {
  const data = JSON.stringify({
    jsonrpc: '2.0',
    id: Date.now(),
    method: method,
    params: params
  });

  const options = {
    hostname: 'localhost',
    port: 3002,
    path: '/',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length
    }
  };

  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        try {
          resolve(JSON.parse(responseData));
        } catch (e) {
          reject(e);
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    req.write(data);
    req.end();
  });
}

async function testMCPServer() {
  console.log('Testing MCP Filesystem Server...\n');

  try {
    // Test 1: List tools
    console.log('1. Testing tools/list...');
    const toolsResponse = await makeRequest('tools/list');
    console.log('Tools available:', toolsResponse.result?.tools?.length || 0);
    if (toolsResponse.result?.tools) {
      toolsResponse.result.tools.forEach(tool => {
        console.log(`   - ${tool.name}: ${tool.description}`);
      });
    }
    console.log();

    // Test 2: List allowed directories
    console.log('2. Testing list_allowed_directories...');
    const dirsResponse = await makeRequest('tools/call', {
      name: 'list_allowed_directories',
      arguments: {}
    });
    console.log('Response:', dirsResponse.result?.content?.[0]?.text || 'No response');
    console.log();

    // Test 3: Create a test directory
    console.log('3. Testing create_directory...');
    const createDirResponse = await makeRequest('tools/call', {
      name: 'create_directory',
      arguments: { path: '/app/managed-fs/test-dir' }
    });
    console.log('Response:', createDirResponse.result?.content?.[0]?.text || 'No response');
    console.log();

    // Test 4: Write a test file
    console.log('4. Testing write_file...');
    const writeFileResponse = await makeRequest('tools/call', {
      name: 'write_file',
      arguments: {
        path: '/app/managed-fs/test-dir/hello.txt',
        content: 'Hello from MCP Filesystem Server!\nThis is a test file.'
      }
    });
    console.log('Response:', writeFileResponse.result?.content?.[0]?.text || 'No response');
    console.log();

    // Test 5: Read the test file
    console.log('5. Testing read_file...');
    const readFileResponse = await makeRequest('tools/call', {
      name: 'read_file',
      arguments: { path: '/app/managed-fs/test-dir/hello.txt' }
    });
    console.log('File content:');
    console.log(readFileResponse.result?.content?.[0]?.text || 'No content');
    console.log();

    // Test 6: List directory contents
    console.log('6. Testing list_directory...');
    const listDirResponse = await makeRequest('tools/call', {
      name: 'list_directory',
      arguments: { path: '/app/managed-fs' }
    });
    console.log('Directory contents:');
    console.log(listDirResponse.result?.content?.[0]?.text || 'No content');
    console.log();

    // Test 7: Get file info
    console.log('7. Testing get_file_info...');
    const fileInfoResponse = await makeRequest('tools/call', {
      name: 'get_file_info',
      arguments: { path: '/app/managed-fs/test-dir/hello.txt' }
    });
    console.log('File info:');
    console.log(fileInfoResponse.result?.content?.[0]?.text || 'No info');
    console.log();

    console.log('✅ All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response);
    }
  }
}

// Run the tests
testMCPServer();
