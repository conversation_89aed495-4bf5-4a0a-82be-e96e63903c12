#!/usr/bin/env pwsh
# Test Synology MCP Server Deployment

param (
    [string]$SynologyHost = "************",
    [string]$ServerName = "markitdown",
    [string]$Port = "3001"
)

Write-Host "Testing Synology MCP Server Deployment" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan
Write-Host "Server: $ServerName" -ForegroundColor Yellow
Write-Host "Host: $SynologyHost" -ForegroundColor Yellow
Write-Host "Port: $Port" -ForegroundColor Yellow
Write-Host ""

# Test 1: Basic connectivity
Write-Host "1. Testing basic connectivity to Synology..." -NoNewline
try {
    $ping = Test-Connection -ComputerName $SynologyHost -Count 1 -Quiet
    if ($ping) {
        Write-Host " ✅ OK" -ForegroundColor Green
    } else {
        Write-Host " ❌ FAILED" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host " ❌ FAILED" -ForegroundColor Red
    exit 1
}

# Test 2: Port accessibility
Write-Host "2. Testing port $Port accessibility..." -NoNewline
try {
    $portTest = Test-NetConnection -ComputerName $SynologyHost -Port $Port -WarningAction SilentlyContinue
    if ($portTest.TcpTestSucceeded) {
        Write-Host " ✅ OK" -ForegroundColor Green
    } else {
        Write-Host " ❌ FAILED" -ForegroundColor Red
        Write-Host "   Port $Port is not accessible on $SynologyHost" -ForegroundColor Red
    }
} catch {
    Write-Host " ❌ FAILED" -ForegroundColor Red
}

# Test 3: HTTP health endpoint
Write-Host "3. Testing health endpoint..." -NoNewline
try {
    $healthUrl = "http://$SynologyHost`:$Port/health"
    $response = Invoke-WebRequest -Uri $healthUrl -TimeoutSec 10 -ErrorAction SilentlyContinue
    
    if ($response.StatusCode -eq 200) {
        Write-Host " ✅ OK" -ForegroundColor Green
        Write-Host "   Status Code: $($response.StatusCode)" -ForegroundColor Green
        
        # Try to parse JSON response
        try {
            $healthData = $response.Content | ConvertFrom-Json
            Write-Host "   Response: $($response.Content)" -ForegroundColor Green
        } catch {
            Write-Host "   Response: $($response.Content)" -ForegroundColor Green
        }
    } else {
        Write-Host " ❌ FAILED" -ForegroundColor Red
        Write-Host "   Status Code: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host " ❌ FAILED" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Root endpoint
Write-Host "4. Testing root endpoint..." -NoNewline
try {
    $rootUrl = "http://$SynologyHost`:$Port/"
    $response = Invoke-WebRequest -Uri $rootUrl -TimeoutSec 10 -ErrorAction SilentlyContinue
    
    if ($response.StatusCode -eq 200) {
        Write-Host " ✅ OK" -ForegroundColor Green
        Write-Host "   Response: $($response.Content)" -ForegroundColor Green
    } else {
        Write-Host " ⚠️  Partial" -ForegroundColor Yellow
        Write-Host "   Status Code: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host " ⚠️  Not available" -ForegroundColor Yellow
}

# Test 5: SSH connection to check Docker status
Write-Host "5. Checking Docker container status via SSH..." -NoNewline
try {
    # Note: This requires SSH key or will prompt for password
    $sshCommand = "ssh swilliams@$SynologyHost 'docker ps --filter name=markitdown-mcp-server --format \"table {{.Names}}\t{{.Status}}\t{{.Ports}}\"'"
    Write-Host " (SSH check)" -ForegroundColor Blue
    Write-Host "   Run this command to check container status:" -ForegroundColor Yellow
    Write-Host "   ssh swilliams@$SynologyHost" -ForegroundColor Gray
    Write-Host "   docker ps --filter name=markitdown-mcp-server" -ForegroundColor Gray
} catch {
    Write-Host " ⚠️  SSH not configured" -ForegroundColor Yellow
}

# Test 6: Test with curl (if available)
Write-Host "6. Testing with curl (if available)..." -NoNewline
try {
    $curlTest = curl -s -o /dev/null -w "%{http_code}" "http://$SynologyHost`:$Port/health" 2>$null
    if ($curlTest -eq "200") {
        Write-Host " ✅ OK" -ForegroundColor Green
    } else {
        Write-Host " ❌ Status: $curlTest" -ForegroundColor Red
    }
} catch {
    Write-Host " ⚠️  curl not available" -ForegroundColor Yellow
}

# Summary and recommendations
Write-Host "`n📊 Test Summary:" -ForegroundColor Cyan
Write-Host "================" -ForegroundColor Cyan

$healthUrl = "http://$SynologyHost`:$Port/health"
try {
    $finalTest = Invoke-WebRequest -Uri $healthUrl -TimeoutSec 5 -ErrorAction SilentlyContinue
    if ($finalTest.StatusCode -eq 200) {
        Write-Host "✅ MCP Server Status: RUNNING" -ForegroundColor Green
        Write-Host "✅ Health Endpoint: ACCESSIBLE" -ForegroundColor Green
        Write-Host "✅ URL: $healthUrl" -ForegroundColor Green
        
        Write-Host "`n🎉 SUCCESS! Your MarkItDown MCP server is running on Synology!" -ForegroundColor Green
        
        Write-Host "`n📋 Next Steps:" -ForegroundColor Yellow
        Write-Host "1. Update VS Code settings to use Synology URL" -ForegroundColor White
        Write-Host "2. Test MCP integration with GitHub Copilot" -ForegroundColor White
        Write-Host "3. Deploy additional MCP servers one by one" -ForegroundColor White
        
        Write-Host "`n⚙️  VS Code Settings Update:" -ForegroundColor Yellow
        Write-Host "Add this to your .vscode/settings.json:" -ForegroundColor White
        Write-Host '{' -ForegroundColor Gray
        Write-Host '  "github.copilot.chat.useMcp": true,' -ForegroundColor Gray
        Write-Host '  "github.copilot.mcpServers": [' -ForegroundColor Gray
        Write-Host '    "markitdown-mcp"' -ForegroundColor Gray
        Write-Host '  ],' -ForegroundColor Gray
        Write-Host '  "markitdown-mcp.url": "http://************:3001"' -ForegroundColor Gray
        Write-Host '}' -ForegroundColor Gray
        
    } else {
        Write-Host "❌ MCP Server Status: NOT RESPONDING" -ForegroundColor Red
        Write-Host "❌ Health Endpoint: FAILED" -ForegroundColor Red
        
        Write-Host "`n🔧 Troubleshooting Steps:" -ForegroundColor Yellow
        Write-Host "1. SSH to Synology and check container:" -ForegroundColor White
        Write-Host "   ssh swilliams@$SynologyHost" -ForegroundColor Gray
        Write-Host "   docker ps -a --filter name=markitdown" -ForegroundColor Gray
        Write-Host "   docker logs markitdown-mcp-server" -ForegroundColor Gray
        
        Write-Host "`n2. Restart container if needed:" -ForegroundColor White
        Write-Host "   docker restart markitdown-mcp-server" -ForegroundColor Gray
        
        Write-Host "`n3. Check if port is bound correctly:" -ForegroundColor White
        Write-Host "   docker port markitdown-mcp-server" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ MCP Server Status: CONNECTION FAILED" -ForegroundColor Red
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    
    Write-Host "`n🔧 Troubleshooting Steps:" -ForegroundColor Yellow
    Write-Host "1. Verify container is running on Synology" -ForegroundColor White
    Write-Host "2. Check firewall settings" -ForegroundColor White
    Write-Host "3. Verify port mapping in docker-compose.yml" -ForegroundColor White
}

Write-Host "`n🔍 Manual Verification Commands:" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan
Write-Host "Test from command line:" -ForegroundColor White
Write-Host "  curl http://$SynologyHost`:$Port/health" -ForegroundColor Gray
Write-Host "  Invoke-WebRequest http://$SynologyHost`:$Port/health" -ForegroundColor Gray
Write-Host ""
Write-Host "Test from browser:" -ForegroundColor White
Write-Host "  http://$SynologyHost`:$Port/health" -ForegroundColor Gray
Write-Host "  http://$SynologyHost`:$Port/" -ForegroundColor Gray

Write-Host "`n✅ Test completed!" -ForegroundColor Green
