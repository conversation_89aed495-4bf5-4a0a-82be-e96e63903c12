# Connect to Synology NAS via SSH for MCP Server Management
param(
    [string]$NasHost = "************",
    [string]$Username = "swilliams",
    [string]$Password = "Flight99!",
    [int]$Port = 22
)

Write-Host "Connecting to Synology NAS for MCP Server Management" -ForegroundColor Green
Write-Host "Host: $NasHost" -ForegroundColor Cyan
Write-Host "User: $Username" -ForegroundColor Cyan

# Test SSH connectivity first
Write-Host "`n1. Testing SSH connectivity..." -ForegroundColor Yellow
try {
    $testConnection = Test-NetConnection -ComputerName $NasHost -Port $Port -WarningAction SilentlyContinue
    if ($testConnection.TcpTestSucceeded) {
        Write-Host "   ✓ SSH port $Port is accessible" -ForegroundColor Green
    } else {
        Write-Host "   ✗ SSH port $Port is not accessible" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "   ✗ Connection test failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Check if we have SSH client available
Write-Host "`n2. Checking SSH client availability..." -ForegroundColor Yellow
$sshPath = Get-Command ssh -ErrorAction SilentlyContinue
if ($sshPath) {
    Write-Host "   ✓ SSH client found at: $($sshPath.Source)" -ForegroundColor Green
} else {
    Write-Host "   ✗ SSH client not found. Please install OpenSSH client." -ForegroundColor Red
    Write-Host "   Install via: Add-WindowsCapability -Online -Name OpenSSH.Client~~~~*******" -ForegroundColor Yellow
    exit 1
}

# Connect to NAS and check Docker containers
Write-Host "`n3. Connecting to NAS and checking MCP containers..." -ForegroundColor Yellow

$sshCommands = @(
    "echo 'Connected to Synology NAS successfully'",
    "docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}' | grep -E '(mcp|7031)' || echo 'No MCP containers found on port 7031'",
    "docker ps -a --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}' | head -10",
    "netstat -tlnp | grep :7031 || echo 'Port 7031 not in use'",
    "ls -la /volume1/docker/ | grep mcp || echo 'No MCP directories found'"
)

foreach ($cmd in $sshCommands) {
    Write-Host "`nExecuting: $cmd" -ForegroundColor Cyan
    try {
        # Use sshpass if available, otherwise prompt for password
        $sshCmd = "ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $Username@$NasHost `"$cmd`""
        Invoke-Expression $sshCmd
    }
    catch {
        Write-Host "Command failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n4. MCP Server Management Commands:" -ForegroundColor Green
Write-Host "To manually connect: ssh $Username@$NasHost" -ForegroundColor Cyan
Write-Host "To check containers: docker ps | grep mcp" -ForegroundColor Cyan
Write-Host "To view logs: docker logs <container-name>" -ForegroundColor Cyan
Write-Host "To restart container: docker restart <container-name>" -ForegroundColor Cyan
