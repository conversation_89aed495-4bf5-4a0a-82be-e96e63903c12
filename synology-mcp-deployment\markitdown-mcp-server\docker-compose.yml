version: '3.8'

services:
  markitdown-mcp-server:
    build:
      context: ../markitdown/packages/markitdown-mcp
      dockerfile: Dockerfile
    container_name: markitdown-mcp-server
    environment:
      - PYTHONUNBUFFERED=1
      - DEBIAN_FRONTEND=noninteractive
    volumes:
      - C:/Users/<USER>/vsproject:/app/workspace:ro
      - markitdown-temp:/tmp
    # Run in stdio mode by default for MCP compatibility
    command: ["markitdown-mcp"]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import markitdown_mcp; print('healthy')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - mcp-network

  # HTTP version for testing and n8n integration
  markitdown-mcp-http:
    build:
      context: ../markitdown/packages/markitdown-mcp
      dockerfile: Dockerfile
    container_name: markitdown-mcp-http
    ports:
      - "8080:8080"
    environment:
      - PYTHONUNBUFFERED=1
      - DEBIAN_FRONTEND=noninteractive
    volumes:
      - C:/Users/<USER>/vsproject:/app/workspace:ro
      - markitdown-temp:/tmp
    # Run in HTTP mode on port 8080
    command: ["markitdown-mcp", "--http", "--host", "0.0.0.0", "--port", "8080"]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/mcp/health", "||", "exit", "1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 15s
    networks:
      - mcp-network

volumes:
  markitdown-temp:

networks:
  mcp-network:
    driver: bridge

