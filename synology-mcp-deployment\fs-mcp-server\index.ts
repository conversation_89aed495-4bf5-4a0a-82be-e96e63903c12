#!/usr/bin/env node
import express, { Request, Response } from "express";
import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { SSEServerTransport } from "@modelcontextprotocol/sdk/server/sse.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";
import fs from "fs/promises";
import path from "path";
import os from 'os';
import { z } from "zod";
import { zodToJsonSchema } from "zod-to-json-schema";
import { createTwoFilesPatch } from 'diff';
import { minimatch } from 'minimatch';
import cors from 'cors';

// Command line argument parsing
const args = process.argv.slice(2);
if (args.length === 0) {
  console.error("Usage: mcp-server-filesystem <allowed-directory> [additional-directories...]");
  process.exit(1);
}

// Normalize all paths consistently
function normalizePath(p: string): string {
  return path.normalize(p);
}

function expandHome(filepath: string): string {
  if (filepath.startsWith('~/') || filepath === '~') {
    return path.join(os.homedir(), filepath.slice(1));
  }
  return filepath;
}

// Store allowed directories in normalized form
const allowedDirectories = args.map(dir =>
  normalizePath(path.resolve(expandHome(dir)))
);

// Validate that all directories exist and are accessible
await Promise.all(args.map(async (dir) => {
  try {
    const stats = await fs.stat(expandHome(dir));
    if (!stats.isDirectory()) {
      console.error(`Error: ${dir} is not a directory`);
      process.exit(1);
    }
  } catch (error) {
    console.error(`Error accessing directory ${dir}:`, error);
    process.exit(1);
  }
}));

// Server configuration
const port = parseInt(process.env.PORT || '3002', 10);
const host = process.env.HOST || '0.0.0.0'; // Use 0.0.0.0 to allow external connections in Docker

// Security utilities
async function validatePath(requestedPath: string): Promise<string> {
  const expandedPath = expandHome(requestedPath);
  const absolute = path.isAbsolute(expandedPath)
    ? path.resolve(expandedPath)
    : path.resolve(process.cwd(), expandedPath);

  const normalizedRequested = normalizePath(absolute);

  // Check if path is within allowed directories
  const isAllowed = allowedDirectories.some(dir => normalizedRequested.startsWith(dir));
  if (!isAllowed) {
    throw new Error(`Access denied - path outside allowed directories: ${absolute} not in ${allowedDirectories.join(', ')}`);
  }

  // Handle symlinks by checking their real path
  try {
    const realPath = await fs.realpath(absolute);
    const normalizedReal = normalizePath(realPath);
    const isRealPathAllowed = allowedDirectories.some(dir => normalizedReal.startsWith(dir));
    if (!isRealPathAllowed) {
      throw new Error("Access denied - symlink target outside allowed directories");
    }
    return realPath;
  } catch (error) {
    // For new files that don't exist yet, verify parent directory
    const parentDir = path.dirname(absolute);
    try {
      const realParentPath = await fs.realpath(parentDir);
      const normalizedParent = normalizePath(realParentPath);
      const isParentAllowed = allowedDirectories.some(dir => normalizedParent.startsWith(dir));
      if (!isParentAllowed) {
        throw new Error("Access denied - parent directory outside allowed directories");
      }
      return absolute;
    } catch {
      throw new Error(`Parent directory does not exist: ${parentDir}`);
    }
  }
}

// Schema definitions
const ReadFileArgsSchema = z.object({
  path: z.string(),
});

const ReadMultipleFilesArgsSchema = z.object({
  paths: z.array(z.string()),
});

const WriteFileArgsSchema = z.object({
  path: z.string(),
  content: z.string(),
});

const EditOperation = z.object({
  oldText: z.string().describe('Text to search for - must match exactly'),
  newText: z.string().describe('Text to replace with')
});

const EditFileArgsSchema = z.object({
  path: z.string(),
  edits: z.array(EditOperation),
  dryRun: z.boolean().default(false).describe('Preview changes using git-style diff format')
});

const CreateDirectoryArgsSchema = z.object({
  path: z.string(),
});

const ListDirectoryArgsSchema = z.object({
  path: z.string(),
});

const DirectoryTreeArgsSchema = z.object({
  path: z.string(),
});

const MoveFileArgsSchema = z.object({
  source: z.string(),
  destination: z.string(),
});

const SearchFilesArgsSchema = z.object({
  path: z.string(),
  pattern: z.string(),
  excludePatterns: z.array(z.string()).optional().default([])
});

const GetFileInfoArgsSchema = z.object({
  path: z.string(),
});

interface FileInfo {
  size: number;
  created: Date;
  modified: Date;
  accessed: Date;
  isDirectory: boolean;
  isFile: boolean;
  permissions: string;
}

// Define available tools
const tools = [
  {
    name: "read_file",
    description: "Read the contents of a file",
    inputSchema: zodToJsonSchema(ReadFileArgsSchema)
  },
  {
    name: "write_file",
    description: "Write content to a file",
    inputSchema: zodToJsonSchema(WriteFileArgsSchema)
  },
  {
    name: "read_multiple_files",
    description: "Read the contents of multiple files",
    inputSchema: zodToJsonSchema(ReadMultipleFilesArgsSchema)
  },
  {
    name: "edit_file",
    description: "Edit a file by replacing text",
    inputSchema: zodToJsonSchema(EditFileArgsSchema)
  },
  {
    name: "create_directory",
    description: "Create a new directory",
    inputSchema: zodToJsonSchema(CreateDirectoryArgsSchema)
  },
  {
    name: "list_directory",
    description: "List contents of a directory",
    inputSchema: zodToJsonSchema(ListDirectoryArgsSchema)
  },
  {
    name: "directory_tree",
    description: "Get a tree representation of a directory",
    inputSchema: zodToJsonSchema(DirectoryTreeArgsSchema)
  },
  {
    name: "move_file",
    description: "Move a file or directory",
    inputSchema: zodToJsonSchema(MoveFileArgsSchema)
  },
  {
    name: "search_files",
    description: "Search for files matching a pattern",
    inputSchema: zodToJsonSchema(SearchFilesArgsSchema)
  },
  {
    name: "get_file_info",
    description: "Get file metadata and information",
    inputSchema: zodToJsonSchema(GetFileInfoArgsSchema)
  },
  {
    name: "list_allowed_directories",
    description: "List all allowed directories",
    inputSchema: {}
  }
];

// Create server instance with capabilities
const serverInfo = {
  name: "secure-filesystem-server",
  version: "0.2.0",
};

// Convert tools to MCP format and create capabilities
const mcp_tools = tools.reduce((acc, tool) => {
  acc[tool.name] = {
    name: tool.name,
    description: tool.description,
    inputSchema: tool.inputSchema
  };
  return acc;
}, {} as Record<string, { name: string; description: string; inputSchema: any; }>);

// Create MCP server with capabilities
const server = new Server(serverInfo, {
  capabilities: {
    tools: {}
  }
});

// Register tool list handler
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: Object.values(mcp_tools)
  };
});

// Set up express server
const app = express();
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(cors());

// Store active transports
const transports: {[sessionId: string]: SSEServerTransport} = {};

// Handle tool calls
async function handleToolCall(toolName: string, args: any): Promise<string> {
  switch (toolName) {
    case 'list_allowed_directories':
      return JSON.stringify(allowedDirectories, null, 2);

    case 'list_directory':
      const dirPath = args.path;
      const validDirPath = await validatePath(dirPath);
      const entries = await fs.readdir(validDirPath, { withFileTypes: true });
      const fileInfos = await Promise.all(
        entries.map(async (entry) => {
          const fullPath = path.join(dirPath, entry.name);
          const stats = await fs.stat(fullPath);
          return {
            name: entry.name,
            type: entry.isDirectory() ? 'directory' : 'file',
            size: stats.size,
            modified: stats.mtime.toISOString()
          };
        })
      );
      return JSON.stringify(fileInfos, null, 2);

    case 'read_file':
      const filePath = args.path;
      const validFilePath = await validatePath(filePath);
      const content = await fs.readFile(validFilePath, 'utf-8');
      return content;

    case 'write_file':
      const writeFilePath = args.path;
      const writeContent = args.content;
      const validWriteFilePath = await validatePath(writeFilePath);
      await fs.writeFile(validWriteFilePath, writeContent, 'utf-8');
      return `File written successfully: ${writeFilePath}`;

    case 'create_directory':
      const createDirPath = args.path;
      const validCreateDirPath = await validatePath(createDirPath);
      await fs.mkdir(validCreateDirPath, { recursive: true });
      return `Directory created successfully: ${createDirPath}`;

    case 'get_file_info':
      const infoFilePath = args.path;
      const validInfoFilePath = await validatePath(infoFilePath);
      const stats = await fs.stat(validInfoFilePath);
      const fileInfo = {
        path: infoFilePath,
        size: stats.size,
        type: stats.isDirectory() ? 'directory' : 'file',
        created: stats.birthtime.toISOString(),
        modified: stats.mtime.toISOString(),
        accessed: stats.atime.toISOString(),
        permissions: stats.mode.toString(8)
      };
      return JSON.stringify(fileInfo, null, 2);

    default:
      throw new Error(`Unknown tool: ${toolName}`);
  }
}

// Simple health check endpoint
app.get("/health", (_req: Request, res: Response) => {
  res.json({ status: "ok", server: "fs-mcp-server" });
});

// Handle JSON-RPC requests directly
app.post("/", async (req: Request, res: Response) => {
  try {
    console.log('Received JSON-RPC request:', req.body);

    // Handle the request based on method
    const { method, params, id } = req.body;

    if (method === 'tools/list') {
      const tools = Object.values(mcp_tools);
      res.json({
        jsonrpc: '2.0',
        result: { tools },
        id
      });
    } else if (method === 'tools/call') {
      // Handle tool calls
      const toolName = params.name;
      const toolArgs = params.arguments || {};

      if (mcp_tools[toolName]) {
        try {
          const result = await handleToolCall(toolName, toolArgs);
          res.json({
            jsonrpc: '2.0',
            result: { content: [{ type: 'text', text: result }] },
            id
          });
        } catch (error) {
          res.json({
            jsonrpc: '2.0',
            error: {
              code: -32000,
              message: error instanceof Error ? error.message : String(error)
            },
            id
          });
        }
      } else {
        res.json({
          jsonrpc: '2.0',
          error: {
            code: -32601,
            message: `Tool not found: ${toolName}`
          },
          id
        });
      }
    } else {
      res.json({
        jsonrpc: '2.0',
        error: {
          code: -32601,
          message: `Method not found: ${method}`
        },
        id
      });
    }
  } catch (err) {
    console.error('Error handling request:', err);
    const errorMessage = err instanceof Error ? err.message : String(err);
    res.status(500).json({
      jsonrpc: '2.0',
      error: {
        code: -32000,
        message: 'Internal server error',
        data: {
          details: errorMessage,
          timestamp: new Date().toISOString()
        }
      },
      id: req.body?.id ?? null
    });
  }
});

app.get("/sse", async (req: Request, res: Response) => {
  console.log('New SSE connection request');

  try {
    // Create transport
    const transport = new SSEServerTransport('/messages', res);
    console.log(`Created new transport with sessionId: ${transport.sessionId}`);
    transports[transport.sessionId] = transport;

    // Handle client disconnection
    req.on("close", () => {
      console.log(`Client disconnected, removing transport ${transport.sessionId}`);
      delete transports[transport.sessionId];
    });

    res.on("close", () => {
      console.log(`Response closed, removing transport ${transport.sessionId}`);
      delete transports[transport.sessionId];
    });

    // Start the transport and connect the server
    await (transport as any).start();
    await server.connect(transport as any);
    console.log('Server connected to transport successfully');
  } catch (error) {
    console.error('Error connecting server to transport:', error);
    if (!res.headersSent) {
      res.status(500).end();
    }
  }
});

app.post("/messages", async (req: Request, res: Response) => {
  const sessionId = req.query.sessionId as string;
  console.log(`Received message for session ${sessionId}:`, JSON.stringify(req.body, null, 2));

  const transport = transports[sessionId];
  if (!transport) {
    console.log(`No transport found for sessionId ${sessionId}`);
    res.status(400).json({ error: 'No transport found for sessionId' });
    return;
  }

  try {
    await transport.handlePostMessage(req, res);
    console.log('Message handled successfully');
  } catch (error) {
    console.error('Error handling message:', error);
    res.status(500).json({ error: 'Error handling message' });
  }
});

app.listen(port, host, () => {
  console.log(`Secure MCP Filesystem Server running on ${host}:${port}`);
  console.log('Allowed directories:', allowedDirectories);
});

// Tool implementations
async function getFileStats(filePath: string): Promise<FileInfo> {
  const stats = await fs.stat(filePath);
  return {
    size: stats.size,
    created: stats.birthtime,
    modified: stats.mtime,
    accessed: stats.atime,
    isDirectory: stats.isDirectory(),
    isFile: stats.isFile(),
    permissions: stats.mode.toString(8).slice(-3),
  };
}

async function searchFiles(
  rootPath: string,
  pattern: string,
  excludePatterns: string[] = []
): Promise<string[]> {
  const results: string[] = [];

  async function search(currentPath: string) {
    const entries = await fs.readdir(currentPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(currentPath, entry.name);

      try {
        // Validate each path before processing
        await validatePath(fullPath);

        // Check if path matches any exclude pattern
        const relativePath = path.relative(rootPath, fullPath);
        const shouldExclude = excludePatterns.some(pattern => {
          const globPattern = pattern.includes('*') ? pattern : `**/${pattern}/**`;
          return minimatch(relativePath, globPattern, { dot: true });
        });

        if (shouldExclude) {
          continue;
        }

        if (entry.name.toLowerCase().includes(pattern.toLowerCase())) {
          results.push(fullPath);
        }

        if (entry.isDirectory()) {
          await search(fullPath);
        }
      } catch (error) {
        // Skip invalid paths during search
        continue;
      }
    }
  }

  await search(rootPath);
  return results;
}

// file editing and diffing utilities
function normalizeLineEndings(text: string): string {
  return text.replace(/\r\n/g, '\n');
}

function createUnifiedDiff(originalContent: string, newContent: string, filepath: string = 'file'): string {
  // Ensure consistent line endings for diff
  const normalizedOriginal = normalizeLineEndings(originalContent);
  const normalizedNew = normalizeLineEndings(newContent);

  return createTwoFilesPatch(
    filepath,
    filepath,
    normalizedOriginal,
    normalizedNew,
    'original',
    'modified'
  );
}

async function applyFileEdits(
  filePath: string,
  edits: Array<{oldText: string, newText: string}>,
  dryRun = false
): Promise<string> {
  // Read file content and normalize line endings
  const content = normalizeLineEndings(await fs.readFile(filePath, 'utf-8'));

  // Apply edits sequentially
  let modifiedContent = content;
  for (const edit of edits) {
    const normalizedOld = normalizeLineEndings(edit.oldText);
    const normalizedNew = normalizeLineEndings(edit.newText);

    // If exact match exists, use it
    if (modifiedContent.includes(normalizedOld)) {
      modifiedContent = modifiedContent.replace(normalizedOld, normalizedNew);
      continue;
    }

    // Otherwise, try line-by-line matching with flexibility for whitespace
    const oldLines = normalizedOld.split('\n');
    const contentLines = modifiedContent.split('\n');
    let matchFound = false;

    for (let i = 0; i <= contentLines.length - oldLines.length; i++) {
      const potentialMatch = contentLines.slice(i, i + oldLines.length);

      // Compare lines with normalized whitespace
      const isMatch = oldLines.every((oldLine, j) => {
        const contentLine = potentialMatch[j];
        return oldLine.trim() === contentLine.trim();
      });

      if (isMatch) {
        // Preserve original indentation of first line
        const originalIndent = contentLines[i].match(/^\s*/)?.[0] || '';
        const newLines = normalizedNew.split('\n').map((line, j) => {
          if (j === 0) return originalIndent + line.trimStart();
          // For subsequent lines, try to preserve relative indentation
          const oldIndent = oldLines[j]?.match(/^\s*/)?.[0] || '';
          const newIndent = line.match(/^\s*/)?.[0] || '';
          if (oldIndent && newIndent) {
            const relativeIndent = newIndent.length - oldIndent.length;
            return originalIndent + ' '.repeat(Math.max(0, relativeIndent)) + line.trimStart();
          }
          return line;
        });

        contentLines.splice(i, oldLines.length, ...newLines);
        modifiedContent = contentLines.join('\n');
        matchFound = true;
        break;
      }
    }

    if (!matchFound) {
      throw new Error(`Could not find exact match for edit:\n${edit.oldText}`);
    }
  }

  // Create unified diff
  const diff = createUnifiedDiff(content, modifiedContent, filePath);

  // Format diff with appropriate number of backticks
  let numBackticks = 3;
  while (diff.includes('`'.repeat(numBackticks))) {
    numBackticks++;
  }
  const formattedDiff = `${'`'.repeat(numBackticks)}diff\n${diff}${'`'.repeat(numBackticks)}\n\n`;

  if (!dryRun) {
    await fs.writeFile(filePath, modifiedContent, 'utf-8');
  }

  return formattedDiff;
}

// Register tool handlers
server.setRequestHandler(CallToolRequestSchema, async (request: { params: { name: string; _meta?: object; arguments?: Record<string, any> } }) => {
  try {
    const { name, arguments: args } = request.params;

    switch (name) {
      case "read_file": {
        const parsed = ReadFileArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for read_file: ${parsed.error}`);
        }
        const validPath = await validatePath(parsed.data.path);
        const content = await fs.readFile(validPath, "utf-8");
        return {
          content: [{ type: "text", text: content }],
        };
      }

      case "read_multiple_files": {
        const parsed = ReadMultipleFilesArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for read_multiple_files: ${parsed.error}`);
        }
        const results = await Promise.all(
          parsed.data.paths.map(async (filePath: string) => {
            try {
              const validPath = await validatePath(filePath);
              const content = await fs.readFile(validPath, "utf-8");
              return `${filePath}:\n${content}\n`;
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : String(error);
              return `${filePath}: Error - ${errorMessage}`;
            }
          }),
        );
        return {
          content: [{ type: "text", text: results.join("\n---\n") }],
        };
      }

      case "write_file": {
        const parsed = WriteFileArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for write_file: ${parsed.error}`);
        }
        const validPath = await validatePath(parsed.data.path);
        await fs.writeFile(validPath, parsed.data.content, "utf-8");
        return {
          content: [{ type: "text", text: `Successfully wrote to ${parsed.data.path}` }],
        };
      }

      case "edit_file": {
        const parsed = EditFileArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for edit_file: ${parsed.error}`);
        }
        const validPath = await validatePath(parsed.data.path);
        const result = await applyFileEdits(validPath, parsed.data.edits, parsed.data.dryRun);
        return {
          content: [{ type: "text", text: result }],
        };
      }

      case "create_directory": {
        const parsed = CreateDirectoryArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for create_directory: ${parsed.error}`);
        }
        const validPath = await validatePath(parsed.data.path);
        await fs.mkdir(validPath, { recursive: true });
        return {
          content: [{ type: "text", text: `Successfully created directory ${parsed.data.path}` }],
        };
      }

      case "list_directory": {
        const parsed = ListDirectoryArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for list_directory: ${parsed.error}`);
        }
        const validPath = await validatePath(parsed.data.path);
        const entries = await fs.readdir(validPath, { withFileTypes: true });
        const formatted = entries
          .map((entry) => `${entry.isDirectory() ? "[DIR]" : "[FILE]"} ${entry.name}`)
          .join("\n");
        return {
          content: [{ type: "text", text: formatted }],
        };
      }

      case "directory_tree": {
        const parsed = DirectoryTreeArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for directory_tree: ${parsed.error}`);
        }

        interface TreeEntry {
          name: string;
          type: 'file' | 'directory';
          children?: TreeEntry[];
        }

        async function buildTree(currentPath: string): Promise<TreeEntry[]> {
          const validPath = await validatePath(currentPath);
          const entries = await fs.readdir(validPath, {withFileTypes: true});
          const result: TreeEntry[] = [];

          for (const entry of entries) {
            const entryData: TreeEntry = {
              name: entry.name,
              type: entry.isDirectory() ? 'directory' : 'file'
            };

            if (entry.isDirectory()) {
              const subPath = path.join(currentPath, entry.name);
              entryData.children = await buildTree(subPath);
            }

            result.push(entryData);
          }

          return result;
        }

        const treeData = await buildTree(parsed.data.path);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(treeData, null, 2)
          }],
        };
      }

      case "move_file": {
        const parsed = MoveFileArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for move_file: ${parsed.error}`);
        }
        const validSourcePath = await validatePath(parsed.data.source);
        const validDestPath = await validatePath(parsed.data.destination);
        await fs.rename(validSourcePath, validDestPath);
        return {
          content: [{ type: "text", text: `Successfully moved ${parsed.data.source} to ${parsed.data.destination}` }],
        };
      }

      case "search_files": {
        const parsed = SearchFilesArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for search_files: ${parsed.error}`);
        }
        const validPath = await validatePath(parsed.data.path);
        const results = await searchFiles(validPath, parsed.data.pattern, parsed.data.excludePatterns);
        return {
          content: [{ type: "text", text: results.length > 0 ? results.join("\n") : "No matches found" }],
        };
      }

      case "get_file_info": {
        const parsed = GetFileInfoArgsSchema.safeParse(args);
        if (!parsed.success) {
          throw new Error(`Invalid arguments for get_file_info: ${parsed.error}`);
        }
        const validPath = await validatePath(parsed.data.path);
        const info = await getFileStats(validPath);
        return {
          content: [{ type: "text", text: Object.entries(info)
            .map(([key, value]) => `${key}: ${value}`)
            .join("\n") }],
        };
      }

      case "list_allowed_directories": {
        return {
          content: [{
            type: "text",
            text: `Allowed directories:\n${allowedDirectories.join('\n')}`
          }],
        };
      }

      default:
        throw new Error(`Unknown tool: ${name}`);
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      content: [{ type: "text", text: `Error: ${errorMessage}` }],
      isError: true,
    };
  }
});