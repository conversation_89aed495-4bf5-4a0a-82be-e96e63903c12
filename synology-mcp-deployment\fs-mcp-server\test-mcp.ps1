$uri = "http://localhost:3003/sse"
$sessionId = $null

Write-Host "Attempting to connect to SSE endpoint: $uri"

try {
    # Create HTTP request with proper headers
    $request = [System.Net.HttpWebRequest]::Create($uri)
    $request.Method = "GET"
    $request.Headers.Add("Accept", "text/event-stream")
    $request.Headers.Add("Cache-Control", "no-cache")
    $request.KeepAlive = $true
    $request.Timeout = 30000  # 30 seconds timeout
    
    Write-Host "Getting response..."
    $response = $request.GetResponse()
    
    if ($response -eq $null) {
        Write-Host "Error: Response is null"
        exit 1
    }
    
    Write-Host "Response status: $($response.StatusCode)"
    Write-Host "Content type: $($response.ContentType)"
    
    $stream = $response.GetResponseStream()
    if ($stream -eq $null) {
        Write-Host "Error: Stream is null"
        exit 1
    }
    
    $reader = New-Object System.IO.StreamReader($stream)
    if ($reader -eq $null) {
        Write-Host "Error: Reader is null"
        exit 1
    }
    
    Write-Host "Connected successfully. Waiting for session ID..."
    
    # Set a timeout for reading the session ID
    $timeout = 10  # seconds
    $startTime = Get-Date
    
    while ($true) {
        # Check timeout
        if (((Get-Date) - $startTime).TotalSeconds -gt $timeout) {
            Write-Host "Timeout waiting for session ID"
            break
        }
        
        # Try to read a line with timeout
        if ($stream.DataAvailable -or $reader.Peek() -ge 0) {
            $line = $reader.ReadLine()
            Write-Host "Received line: $line"
            
            if ($line -match "^event: endpoint$") {
                Write-Host "Found endpoint event"
                # Read the next line for data
                if ($stream.DataAvailable -or $reader.Peek() -ge 0) {
                    $dataLine = $reader.ReadLine()
                    Write-Host "Data line: $dataLine"
                    
                    if ($dataLine -match "data: (.+)") {
                        $jsonData = $matches[1]
                        Write-Host "JSON data: $jsonData"
                        
                        try {
                            $data = $jsonData | ConvertFrom-Json
                            if ($data.uri -match "sessionId=([^&]+)") {
                                $sessionId = $matches[1]
                                Write-Host "Extracted session ID: $sessionId"
                                break
                            }
                        }
                        catch {
                            Write-Host "Error parsing JSON: $_"
                        }
                    }
                }
            }
            elseif ($line -match "data: (.+)") {
                $jsonData = $matches[1]
                Write-Host "Direct data line: $jsonData"
                
                try {
                    $data = $jsonData | ConvertFrom-Json
                    if ($data.uri -match "sessionId=([^&]+)") {
                        $sessionId = $matches[1]
                        Write-Host "Extracted session ID from direct data: $sessionId"
                        break
                    }
                }
                catch {
                    Write-Host "Error parsing JSON: $_"
                }
            }
        }
        else {
            Start-Sleep -Milliseconds 100
        }
    }
    
    if ($sessionId -eq $null) {
        Write-Host "Failed to get session ID. Exiting."
        exit 1
    }
    
    Write-Host "`nSession ID obtained: $sessionId"
    Write-Host "Now sending list_tools request..."
    
    # Create the JSON-RPC request
    $body = @{
        jsonrpc = "2.0"
        method = "tools/list"
        id = 1
    } | ConvertTo-Json -Depth 3
    
    Write-Host "Request body: $body"
    
    # Send the request
    $messagesUri = "http://localhost:3000/messages?sessionId=$sessionId"
    Write-Host "Sending to: $messagesUri"
    
    $result = Invoke-RestMethod `
        -Uri $messagesUri `
        -Method Post `
        -ContentType "application/json" `
        -Body $body `
        -ErrorAction Stop
    
    Write-Host "`nResponse received:"
    $result | ConvertTo-Json -Depth 10
    
}
catch {
    Write-Host "Error occurred: $($_.Exception.Message)"
    Write-Host "Error details: $($_.Exception)"
    if ($_.Exception.InnerException) {
        Write-Host "Inner exception: $($_.Exception.InnerException.Message)"
    }
}
finally {
    # Clean up resources
    if ($reader) { $reader.Close() }
    if ($stream) { $stream.Close() }
    if ($response) { $response.Close() }
}

Write-Host "`nScript completed. Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")