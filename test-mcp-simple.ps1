#!/usr/bin/env pwsh
# Simple MCP Server Test

param (
    [string]$SynologyHost = "************",
    [string]$Port = "3001"
)

Write-Host "Testing MarkItDown MCP Server on Synology" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host "Target: http://$SynologyHost`:$Port" -ForegroundColor Yellow
Write-Host ""

# Test 1: Basic connectivity
Write-Host "1. Testing basic connectivity..." -NoNewline
try {
    $ping = Test-Connection -ComputerName $SynologyHost -Count 1 -Quiet
    if ($ping) {
        Write-Host " OK" -ForegroundColor Green
    } else {
        Write-Host " FAILED" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host " FAILED" -ForegroundColor Red
    exit 1
}

# Test 2: Port accessibility
Write-Host "2. Testing port $Port..." -NoNewline
try {
    $portTest = Test-NetConnection -ComputerName $SynologyHost -Port $Port -WarningAction SilentlyContinue
    if ($portTest.TcpTestSucceeded) {
        Write-Host " OK" -ForegroundColor Green
    } else {
        Write-Host " FAILED" -ForegroundColor Red
    }
} catch {
    Write-Host " FAILED" -ForegroundColor Red
}

# Test 3: Health endpoint
Write-Host "3. Testing health endpoint..." -NoNewline
try {
    $healthUrl = "http://$SynologyHost`:$Port/health"
    $response = Invoke-WebRequest -Uri $healthUrl -TimeoutSec 10 -ErrorAction SilentlyContinue
    
    if ($response.StatusCode -eq 200) {
        Write-Host " OK" -ForegroundColor Green
        Write-Host "   Status: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "   Response: $($response.Content)" -ForegroundColor Green
    } else {
        Write-Host " FAILED" -ForegroundColor Red
        Write-Host "   Status: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host " FAILED" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Root endpoint
Write-Host "4. Testing root endpoint..." -NoNewline
try {
    $rootUrl = "http://$SynologyHost`:$Port/"
    $response = Invoke-WebRequest -Uri $rootUrl -TimeoutSec 10 -ErrorAction SilentlyContinue
    
    if ($response.StatusCode -eq 200) {
        Write-Host " OK" -ForegroundColor Green
        Write-Host "   Response: $($response.Content)" -ForegroundColor Green
    } else {
        Write-Host " Partial" -ForegroundColor Yellow
        Write-Host "   Status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host " Not available" -ForegroundColor Yellow
}

# Summary
Write-Host "`nTest Summary:" -ForegroundColor Cyan
Write-Host "=============" -ForegroundColor Cyan

$healthUrl = "http://$SynologyHost`:$Port/health"
try {
    $finalTest = Invoke-WebRequest -Uri $healthUrl -TimeoutSec 5 -ErrorAction SilentlyContinue
    if ($finalTest.StatusCode -eq 200) {
        Write-Host "SUCCESS: MCP Server is running!" -ForegroundColor Green
        Write-Host "Health URL: $healthUrl" -ForegroundColor Green
        
        Write-Host "`nNext Steps:" -ForegroundColor Yellow
        Write-Host "1. Update VS Code settings" -ForegroundColor White
        Write-Host "2. Test with GitHub Copilot" -ForegroundColor White
        Write-Host "3. Deploy more MCP servers" -ForegroundColor White
        
        Write-Host "`nManual Test Commands:" -ForegroundColor Yellow
        Write-Host "Browser: http://$SynologyHost`:$Port/health" -ForegroundColor Gray
        Write-Host "PowerShell: Invoke-WebRequest http://$SynologyHost`:$Port/health" -ForegroundColor Gray
        
    } else {
        Write-Host "FAILED: MCP Server not responding" -ForegroundColor Red
        
        Write-Host "`nTroubleshooting:" -ForegroundColor Yellow
        Write-Host "1. SSH to Synology: ssh swilliams@$SynologyHost" -ForegroundColor White
        Write-Host "2. Check container: docker ps -a" -ForegroundColor White
        Write-Host "3. View logs: docker logs markitdown-mcp-server" -ForegroundColor White
        Write-Host "4. Restart: docker restart markitdown-mcp-server" -ForegroundColor White
    }
} catch {
    Write-Host "FAILED: Cannot connect to MCP Server" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    Write-Host "`nTroubleshooting:" -ForegroundColor Yellow
    Write-Host "1. Verify container is running on Synology" -ForegroundColor White
    Write-Host "2. Check firewall settings" -ForegroundColor White
    Write-Host "3. Verify port mapping" -ForegroundColor White
}

Write-Host "`nTest completed!" -ForegroundColor Green
