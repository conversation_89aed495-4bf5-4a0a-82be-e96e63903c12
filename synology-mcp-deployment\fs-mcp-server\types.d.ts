declare module '@modelcontextprotocol/sdk' {
  export interface Transport {
    sessionId?: string;
    connect(): Promise<void>;
    start(): Promise<void>;
    send(data: any): Promise<void>;
    close(): Promise<void>;
    handlePostMessage?(req: any, res: any): Promise<void>;
  }

  export interface ServerInfo {
    name: string;
    version: string;
  }

  export interface ServerConfig {
    capabilities: any;
  }

  export interface ClientInfo {
    name: string;
    version: string;
  }

  export interface ClientConfig {
    capabilities: any;
  }

  export class SSEServerTransport implements Transport {
    sessionId: string;
    private response: any;
    private path: string;

    constructor(path: string, res: any) {
      this.path = path;
      this.response = res;
      this.sessionId = crypto.randomUUID();
    }

    async connect(): Promise<void> {
      this.response.write('connected\n\n');
    }

    async start(): Promise<void> {
      // Keep-alive is handled by heartbeat in the server
      return Promise.resolve();
    }

    async send(data: any): Promise<void> {
      if (!this.response.writableEnded) {
        this.response.write(`data: ${JSON.stringify(data)}\n\n`);
      }
    }

    async close(): Promise<void> {
      if (!this.response.writableEnded) {
        this.response.end();
      }
    }

    async handlePostMessage(req: any, res: any): Promise<void> {
      await this.send(req.body);
      res.json({ success: true });
    }
  }
  
  export class SSEClientTransport implements Transport {
    private eventSource: EventSource | null;
    private serverUrl: string;
    
    constructor(serverUrl: string) {
      this.serverUrl = serverUrl;
      this.eventSource = null;
    }

    async connect(): Promise<void> {
      this.eventSource = new EventSource(this.serverUrl);
      return Promise.resolve();
    }

    async start(): Promise<void> {
      return Promise.resolve();
    }

    async send(data: any): Promise<void> {
      if (this.eventSource) {
        const response = await fetch(this.serverUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data)
        });
        if (!response.ok) {
          throw new Error(`Failed to send message: ${response.statusText}`);
        }
      }
    }

    async close(): Promise<void> {
      if (this.eventSource) {
        this.eventSource.close();
        this.eventSource = null;
      }
    }
  }

  export class Server {
    constructor(serverInfo: ServerInfo, config: ServerConfig);
    setRequestHandler(schema: any, handler: any): void;
    connect(transport: Transport): Promise<void>;
    handleRequest(request: any): Promise<any>;
  }

  export class Client {
    constructor(clientInfo: ClientInfo, config: ClientConfig);
    connect(transport: Transport): Promise<void>;
    invoke(method: string, params?: any): Promise<any>;
    disconnect(): Promise<void>;
  }
}

declare module '@modelcontextprotocol/sdk/types.js' {
  export const CallToolRequestSchema: any;
  export const ListToolsRequestSchema: any;
  export const ToolSchema: any;
}

declare module '@modelcontextprotocol/sdk/server/sse.js' {
  export class SSEServerTransport {
    constructor(path: string, res: any);
    sessionId: string;
    handlePostMessage(req: any, res: any): Promise<void>;  }
}

declare module '@modelcontextprotocol/sdk/transports' {
  import { Transport } from '@modelcontextprotocol/sdk';
  export class SSEClientTransport implements Transport {
    constructor(url: string);
    connect(): Promise<void>;
  }
}
