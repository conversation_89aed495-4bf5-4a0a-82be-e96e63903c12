#!/bin/bash
set -e

# Function to handle test mode
test_mode() {
    echo "Testing Filesystem MCP Server..."
    
    # Test basic functionality
    echo "✓ Container is running"
    echo "✓ Node.js version: $(node --version)"
    echo "✓ Working directory: $(pwd)"
    echo "✓ Available directories: $@"
    
    # Test if the server can start (dry run)
    echo "✓ Testing server startup..."
    timeout 5s node dist/index.js "$@" &
    SERVER_PID=$!
    sleep 2
    
    if kill -0 $SERVER_PID 2>/dev/null; then
        echo "✓ Server starts successfully"
        kill $SERVER_PID 2>/dev/null || true
    else
        echo "✗ Server failed to start"
        exit 1
    fi
    
    echo "✓ All tests passed!"
    exit 0
}

# Function to handle stdio mode (for MCP clients)
stdio_mode() {
    echo "Starting Filesystem MCP Server in stdio mode..." >&2
    exec node dist/index.js "$@"
}

# Function to handle HTTP mode (default)
http_mode() {
    echo "Starting Filesystem MCP Server in HTTP mode..."
    echo "Allowed directories: $@"
    echo "Server will be available at http://0.0.0.0:${PORT:-3002}"
    exec node dist/index.js "$@"
}

# Main logic
case "${1:-}" in
    "test")
        shift
        test_mode "$@"
        ;;
    "stdio")
        shift
        stdio_mode "$@"
        ;;
    *)
        http_mode "$@"
        ;;
esac
