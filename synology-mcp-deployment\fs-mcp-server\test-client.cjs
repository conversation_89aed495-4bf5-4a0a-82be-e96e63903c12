const path = require('path');
const EventSource = require('eventsource');
const sdk = require(path.join(__dirname, 'node_modules/@modelcontextprotocol/sdk/dist/client/index.js'));
const sseTransport = require(path.join(__dirname, 'node_modules/@modelcontextprotocol/sdk/dist/client/sse.js'));

global.EventSource = EventSource;

async function main() {
    const client = new sdk.Client({
        name: "test-client",
        version: "1.0.0"
    }, {
        capabilities: {}
    });

    console.log('Creating SSE transport...');
    const transport = new sseTransport.SSEClientTransport(
        new URL("http://localhost:3003/sse")
    );

    console.log('Connecting to server...');
    try {
        await client.connect(transport);
        console.log('Successfully connected to server');
        
        // Test directory listing
        const result = await client.invoke('listDirectory', {
            directoryPath: '.'
        });
        console.log('Directory listing result:', result);
        
    } catch (error) {
        console.error('Error connecting to server:', error);
        process.exit(1);
    } finally {
        try {
            await client.disconnect();
            console.log('Disconnected from server');
        } catch (error) {
            console.error('Error disconnecting:', error);
        }
    }
}

main().catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
});
